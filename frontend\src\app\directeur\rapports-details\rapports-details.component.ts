import { <PERSON>mpo<PERSON>, OnInit, ChangeDetector<PERSON>ef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, FormsModule,ReactiveFormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';


import { DemandeService } from '../../receptionniste/demandes/demande.service';

import { ActivatedRoute } from '@angular/router';
import { RapportsService } from './rapports.service';
import { Demande } from '../../receptionniste/demandes/demande.model';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPrint, faCheck, faTimes, faSpinner } from '@fortawesome/free-solid-svg-icons';

interface LabInfo {
  nom: string;
  adresse: string;
  contact: string;
}
interface Analysis {
  id: number;
  code_echantillon: string;
  parametre: string;
  mesurande: string | null;
  unite: string | null;
  limite_acceptabilite: string | null;
  methode_analyse_utilisee: string;
  date_analyse: string | null;
  sample: {
    id: number;
    nature_echantillon: string;
    provenance: string;
    masse_echantillon: number;
    etat: string;
    lot: string;
    nom_preleveur: string;
    reference: string;
    date_prelevement: string;
    origine_prelevement: string;
    site: string;
  };
}

// Interface for the report data
interface ReportData {
  rapport_id: number;
  demande_id: number;
  creation_date: string;
  status: string;
  validation: number;
  demande_numero: string;
  analyses: Analysis[];
}
@Component({
  selector: 'app-rapport-danalyse',
  standalone: true,
  imports: [FormsModule, CommonModule, DatePipe, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './rapports-details.component.html',
  styleUrls: ['./rapports-details.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class RapportsDetailsDirectorComponent implements OnInit {
  reportData: any = null;
  clientInfo: any = [];
  samples: any[] = [];
  requestedAnalyses: any[] = [];
  analysisResults: any[] = [];
  hasAccreditedAnalysis: boolean = false;
  hideButtons: boolean = false;
  userCache: { [key: number]: any } = {}; // Stores full user data
  demandeSamples: any[] = [];
  uniqueParameters: string[] = []; // ✅ Unique parameters from analyses
  standardMethodAnalyses: any[] = []; // ✅ Analyses with "Standard Method"
  delaiSouhaite: string | null = null; // ✅ To store delai_souhaite from demande
  analyses_accredite: number | null = null; // ✅ To store delai_souhaite from demande
  validation: number | null = null; // ✅ To store delai_souhaite from demande
  editingIndex: number | null = null;
  labInfo: LabInfo = {
    nom: 'Biotechnologie Bleue et Bioproduits Aquatiques - B³Aqua',
    adresse: 'INSTIM Port de Pêche La Goulette',
    contact: '216 71 735 848'
  };
  faPrint = faPrint;
  faCheck = faCheck;
  faTimes = faTimes;
  faSpinner = faSpinner;
  isEditing: { [key: number]: boolean } = {}; // ✅ Track edit state per row
  analysisForm!: FormGroup; // ✅ Reactive form
  reportid: number | null = null;
  ficheTransmissionId: number | null = null;
  showConfirmModal: boolean = false;
  showLoadingModal: boolean = false;
  confirmAction: string = ''; // 'validate' or 'reject'
  constructor(
    public dialog: MatDialog, private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private reportService: RapportsService,
    private demandeService: DemandeService,
   private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.reportid = params['id'];
      if (this.reportid) {
        this.loadRapportDetails(this.reportid);
      }
    }); // Load a report when the component initializes
  }
  get maxRows(): number {
    return Math.max(this.uniqueParameters.length, this.standardMethodAnalyses.length || 1);
  }
  getProposedMethod(param: string): string {
    const analysis = this.analysisResults.find(a => a.parametre === param);
    return analysis ? analysis.methode_analyse_utilisee : '-';
  }
  /**
   * Fetches the report details and then retrieves the associated user details.
   */
  loadRapportDetails(rapportId: number) {
    this.reportService.getRapportDetails(rapportId).subscribe({
      next: (data: ReportData) => {
        this.reportData = data;
        this.samples = this.extractSamples(data.analyses);
        this.analysisResults = data.analyses;
        this.requestedAnalyses = this.groupAnalysesBySample(data.analyses);

        // ✅ Filter analyses with "Méthode standard" first
        this.standardMethodAnalyses = data.analyses.filter(a => a.methode_analyse_utilisee === "Méthode standard");

        // ✅ Extract unique parameters, excluding those with "Méthode standard"
        const standardMethodParams = new Set(this.standardMethodAnalyses.map(a => a.parametre));
        this.uniqueParameters = [...new Set(
          data.analyses
            .filter(a => a.methode_analyse_utilisee !== "Méthode standard")
            .map(a => a.parametre)
        )];

        this.hasAccreditedAnalysis = this.analysisResults.some(r => r.isAccredited);
        this.initializeForm();
        if (this.reportData.demande_id) {
          this.fetchDemandeUserDetails(this.reportData.demande_id);
        } else {
          console.warn('No demande_id found in report.');
        }
      },
      error: () => {
        console.error(`Error fetching report details for ID ${rapportId}`);
      }
    });
  }
  formatDate(date: string): string {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }
  printRapport(): void {
    if (!this.reportData) {
      console.error('No report data available to print.');
      return;
    }

    const formattedDate = this.formatDate(this.reportData.creation_date);
    const status = this.validation === 1 ? 'Approuvé' : this.validation === 0 ? 'Non Approuvé' : '';
    const printHtml = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Rapport d'Analyse</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 5mm 5mm;
            }
            body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
          }
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 12pt;
          }
          .container {
            width: 100%;
            margin: 0 auto;
            padding: 10px;
          }
          .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-top: 4px double black;
            border-bottom: 4px double black;
            padding: 10px 0;
          }
          .logo-left {
            width: 100px;
            height: auto;
          }
          .log {
            width: 130px;
            height: auto;
            padding-bottom:10px;
          }
          .logo-right {
            width: 50px;
            height: auto;
          }
          .centered {
            text-align: center;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
          }
          th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          .italic {
            font-style: italic;
          }
          .bold {
            font-weight: bold;
          }
          .footer {
            text-align: right;
            margin-top: 20px;
          }
          .small-text {
            font-size: 15pt;
          }
          .double-border {
            border: 2px solid black;
          }
          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Header -->
          <div class="centered">
            <p class="bold" style="font-size: 13px">RÉPUBLIQUE TUNISIENNE</p>
            <p class="bold" style="font-size: 13px">Ministère de l'Agriculture et des Ressources Hydrauliques</p>
            <p class="italic" style="font-size: 12px" >Institution de la Recherche et de l'Enseignement Supérieur Agricoles</p>
            <p class="italic" style="font-size: 12px">Institut National des Sciences et Technologies de la Mer</p>
          </div>

          <!-- Header section with two divs side by side below -->
          <div class="header">
            <div>
              <img src="${window.location.origin}/kls.png" alt="Logo Left" class="logo-left" style=" width: 120px;
            height: auto;">

            </div>
            <div class="header-content">
              ${this.analyses_accredite === 1 ?
                `<img src="${window.location.origin}/Image2.png" alt="Logo Right" class="logo-right" style="margin-right: 30px; width: 110px;
            height: auto;">` :
                `<div class="logo-right" style="margin-right: 30px;"></div>`}
              <div class="text-content">
                <p class="bold">CODE: PE/01-PET/09</p>
                <p class="bold">VERSION: 07</p>
                <p class="bold">DATE: ${formattedDate}</p>
                <p class="bold">PAGE: 1/1</p>
              </div>
            </div>
          </div>
          <p class="bold" style="text-align: center;">RAPPORT D'ANALYSE N° ${this.reportData.rapport_id}</p>
          ${status ? `
            <div style="text-align: center; margin: 10px 0; padding: 5px; border: 2px solid ${status === 'Approuvé' ? 'green' : 'red'}; border-radius: 5px;">
              <p class="bold" style="margin: 0; color: ${status === 'Approuvé' ? 'green' : 'red'}; font-size: 16px;">Statut : ${status}</p>
            </div>
          ` : ''}
          <!-- Client Information -->
          <table class="double-border">
            <tr>
              <td style="width: 50%;">
                <p><strong>Client</strong></p>
                <p>Nom et Prénom : ${this.clientInfo.name || '-'}  ${this.clientInfo.nickname || '-'}</p>
                <p>Adresse : ${this.clientInfo.adress || '-'}</p>
                <p>Telephone : ${this.clientInfo.phone || '-'}</p>
              </td>
              <td style="width: 50%;">
                <p><strong>Laboratoire d'Analyse</strong></p>
                <p>${this.labInfo.nom}</p>
                <p>${this.labInfo.adresse}</p>
                <p>${this.labInfo.contact}</p>
              </td>
            </tr>
          </table>

          <!-- Description of Sample -->
          <h3 class="bold">DESCRIPTION DE L'ÉCHANTILLON :</h3>
          <table class="double-border">
            <thead>
              <tr>
                <th>Code échantillon (Client)</th>
                <th>Date de réception</th>
                <th>Nature</th>
                <th>Provenance</th>
                <th>Origine du prélèvement</th>
                <th>Date de prélèvement</th>
                <th>Site</th>
                <th>Nom du préleveur</th>
                <th>Lot</th>
                <th>Référence</th>
              </tr>
            </thead>
            <tbody>
              ${this.samples.map(sample => `
                <tr>
                  <td>${sample.identification_echantillon}</td>
                  <td>${this.formatDate(sample.reception_date)}</td>
                  <td>${sample.nature_echantillon}</td>
                  <td>${sample.provenance}</td>
                  <td>${sample.origine_prelevement}</td>
                  <td>${this.formatDate(sample.date_prelevement)}</td>
                  <td>${sample.site}</td>
                  <td>${sample.nom_preleveur}</td>
                  <td>${sample.lot}</td>
                  <td>${sample.reference}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          <!-- Analyses Demanded -->
          <h3 class="bold">ANALYSES DEMANDÉES :</h3>
          <table class="double-border">
            <thead>
              <tr>
                <th>N°</th>
                <th>Paramètre</th>
                <th>Méthode d’Analyse proposée</th>
                <th>Délai d'exécution souhaité</th>
              </tr>
            </thead>
            <tbody>
              ${this.uniqueParameters.length > 0 ? this.uniqueParameters.map((param, i) => `
                <tr>
                  <td>${('0' + (i + 1)).slice(-2)}</td>
                  <td>${param}</td>
                  <td style="${this.standardMethodAnalyses.length === 0
                    ? 'border: none; text-align: center;'
                    : (i < this.standardMethodAnalyses.length
                        ? 'text-align: left;'
                        : 'border: none;')}">
                    ${this.standardMethodAnalyses.length === 0
                      ? (i === 0 ? 'Non spécifié' : '')
                      : (i < this.standardMethodAnalyses.length ? this.standardMethodAnalyses[i].parametre : '')}
                  </td>
                  ${i === 0 ? `
                    <td rowspan="${this.uniqueParameters.length}" style="text-align: center;">
                      ${this.delaiSouhaite || 'Non spécifié'}
                    </td>
                  ` : ''}
                </tr>
              `).join('') : `
                <tr>
                  <td>01</td>
                  <td>Non spécifié</td>
                  <td style="border: none; text-align: center;">Non spécifié</td>
                  <td style="text-align: center;">${this.delaiSouhaite || '-'}</td>
                </tr>
              `}
            </tbody>
          </table>

          <!-- Analysis Results -->
          <h3 class="bold">RÉSULTATS D'ANALYSES</h3>
          <table class="double-border">
            <thead>
              <tr>
                <th>Code échantillon</th>
                <th>Paramètre</th>
                <th>Mesurande</th>
                <th>Unité</th>
                <th>Limite d'acceptabilité</th>
                <th>Méthode d'Analyse utilisée</th>
                <th>Date de l'analyse</th>
              </tr>
            </thead>
            <tbody>
              ${this.analysisResults.map(result => `
                <tr>
                  <td>${result.code_echantillon}</td>
                  <td>${result.parametre}</td>
                  <td>${result.mesurande || ''}</td>
                  <td>${result.unite || ''}</td>
                  <td>${result.limite_acceptabilite || ''}</td>
                  <td>${result.methode_analyse_utilisee}</td>
                  <td>${result.date_analyse ? this.formatDate(result.date_analyse) : ''}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Footer -->
          <div class="footer">
            <p class="small-text" style="text-align: left;">(*) La valeur trouvée est inférieure à la limite de quantification de la méthode</p>
            <p class="bold">Date: ${formattedDate}</p>
            <p style="text-align: center; font-weight: bold; font-size: 20px; padding-bottom: 90px;">Le Directeur</p>
            <p style="text-align: center; font-weight: bold; font-size: 15px;">Fin de rapport</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHtml);
      printWindow.document.close();
      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
          // printWindow.close(); // Uncomment if you want the window to close after printing
        }, 500);
      };
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }
  initializeForm() {
    this.analysisForm = this.fb.group({});
    this.analysisResults.forEach((result, index) => {
      this.analysisForm.addControl(`mesurande_${index}`, new FormControl({ value: result.mesurande || '', disabled: true }));
      this.analysisForm.addControl(`unite_${index}`, new FormControl({ value: result.unite || '', disabled: true }));
      this.analysisForm.addControl(`limite_acceptabilite_${index}`, new FormControl({ value: result.limite_acceptabilite || '', disabled: true }));
      this.analysisForm.addControl(`date_analyse_${index}`, new FormControl({ value: result.date_analyse || '', disabled: true }));
      this.isEditing[index] = false; // Initially, all rows are not in edit mode
    });
  }
  toggleEdit(index: number): void {
    this.isEditing[index] = !this.isEditing[index]; // ✅ Toggle edit mode

    const controls = [
      `mesurande_${index}`,
      `unite_${index}`,
      `limite_acceptabilite_${index}`,
      `date_analyse_${index}`
    ];

    controls.forEach(controlName => {
      const control = this.analysisForm.get(controlName);
      if (control) {
        this.isEditing[index] ? control.enable() : control.disable();
      }
    });

    this.cdr.detectChanges(); // ✅ Force UI update
  }


  /**
   * Fetches the `demande` details using `demande_id` to retrieve `user_id`, then fetches user details.
   */
  fetchDemandeUserDetails(demande_id: number): void {
    if (!demande_id) {
      this.clientInfo = { name: '⏳ Chargement...', email: '', phone: '', address: '' };
      return;
    }

    this.demandeService.getDemandeBy(demande_id).subscribe({
      next: (response:Demande) => {
        const demande = response;
        console.log('demande response:', response);
        if (demande && demande.user_id) {
          this.fetchUserDetails(demande.user_id);
        } else {
          console.warn(`No user_id found in demande ${demande_id}`);
          this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
        }
        this.delaiSouhaite = demande.delai_souhaite || null;
        this.analyses_accredite=demande.analyses_accredite ?? null;
        this.validation=demande.validation ?? null;
        console.log('accred',this.validation);
        console.log('delai',this.delaiSouhaite);
      },
      error: () => {
        console.error(`Error fetching demande ${demande_id}`);
        this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
      }
    });
  }


  /**
   * Fetches user details using `user_id`, caches the data, and updates `clientInfo`.
   */
  fetchUserDetails(user_id: number): void {


    this.demandeService.getUserDetails(user_id).subscribe({
      next: (response) => {
        this.clientInfo=response;
        console.log('Client info:', this.clientInfo);
          this.cdr.detectChanges(); // ✅ Force Angular to detect changes

      },
      error: () => {
        console.error(`Error fetching user ${user_id}`);

      }
    });
  }


  /**
   * Groups analyses by sample code.
   */
  private groupAnalysesBySample(analyses: any[]) {
    const grouped: { [key: string]: any[] } = {};
    analyses.forEach((analysis) => {
      if (!grouped[analysis.code_echantillon]) {
        grouped[analysis.code_echantillon] = [];
      }
      grouped[analysis.code_echantillon].push(analysis);
    });

    return Object.entries(grouped).map(([key, value]) => ({
      code_echantillon: key,
      analyses: value
    }));
  }

  /**
   * Extracts unique samples from the analyses.
   */
  private extractSamples(analyses: any[]) {
    const uniqueSamples: { [key: string]: any } = {};
    analyses.forEach((analysis) => {
      const code = analysis.code_echantillon;
      if (!uniqueSamples[code] && analysis.sample) {
        uniqueSamples[code] = {
          identification_echantillon: code,
          reception_date: this.reportData?.creation_date, // Using report creation_date as a proxy; adjust if a specific sample reception date exists
          nature_echantillon: analysis.sample.nature_echantillon,
          provenance: analysis.sample.provenance,
          origine_prelevement: analysis.sample.origine_prelevement,
          date_prelevement: analysis.sample.date_prelevement,
          site: analysis.sample.site,
          lot: analysis.sample.lot,
          nom_preleveur: analysis.sample.nom_preleveur,
          reference: analysis.sample.reference,
        };
      }
    });
    return Object.values(uniqueSamples);
  }

  /**
   * Opens the accreditation confirmation modal.
   */

  // Validate rapport
  validateRapport() {
    this.confirmAction = 'validate';
    this.showConfirmModal = true;
  }

  // Reject rapport
  rejectRapport() {
    this.confirmAction = 'reject';
    this.showConfirmModal = true;
  }

  // Confirm action (validate or reject)
  confirmActionExecution() {
    this.showConfirmModal = false;
    this.showLoadingModal = true;

    if (this.confirmAction === 'validate') {
      this.executeValidateRapport();
    } else if (this.confirmAction === 'reject') {
      this.executeRejectRapport();
    }
  }

  // Cancel confirmation
  cancelAction() {
    this.showConfirmModal = false;
  }

  // Execute validate rapport
  executeValidateRapport() {
    if (!this.reportid) return;

    this.reportService.updateRapportStatusValidate(this.reportid).subscribe(
      (response) => {
        console.log('Rapport validé:', response);
        this.showLoadingModal = false;
        if (this.reportData) {
          this.reportData.validation = 1;
          this.cdr.detectChanges();
        }
      },
      (error) => {
        console.error('Erreur lors de la validation du rapport:', error);
        this.showLoadingModal = false;
      }
    );
  }

  // Execute reject rapport
  executeRejectRapport() {
    if (!this.reportid) return;

    this.reportService.updateRapportStatusReject(this.reportid).subscribe(
      (response) => {
        console.log('Rapport rejeté:', response);
        this.showLoadingModal = false;
        if (this.reportData) {
          this.reportData.validation = 0;
          this.cdr.detectChanges();
        }
      },
      (error) => {
        console.error('Erreur lors du rejet du rapport:', error);
        this.showLoadingModal = false;
      }
    );
  }
}
