<?php

namespace App\Models;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use App\Models\Result;

class Demande extends Model
{

    protected $fillable = ['status',
    'demande_id',
    'total_enregistrements',
     'demande_date',
     'user_id',
    'mode_reglement',
    'rapport_created',
    'devis_sent',
    'facture_id',
    'registre_id',
    'fiche_id',
    'delai_souhaite',
    'analyses_accredite',
    'validation',
    'result_id',
    'payment_id'];

    public function samples()
{
    return $this->hasMany(Sample::class, 'demande_id');
}
public function fiches()
{
    return $this->hasMany(Fiche::class, 'demande_id');
}
public function user()
{
    return $this->belongsTo(User::class);
}
public function facture()
{
    return $this->belongsTo(Facture::class); // facture_id is in demandes table
}
public function broadcastOn()
{
    return new PrivateChannel('demandes.' . $this->user_id);
}
// public function devis()
// {
//     return $this->hasMany(Devis::class);
// }
public function devis()
    {
        return $this->hasMany(Devis::class, 'demande_id', 'id');
    }

public function derogations()
{
    return $this->hasMany(Derogation::class);
}

/**
 * Get the payment associated with the demande.
 */
public function payment()
{
    return $this->belongsTo(Payment::class);
}
public function scopeByDemandeId($query, $demandeId)
    {
        return $query->where('demande_id', $demandeId);
    }

    // Scope to search by id
    public function scopeById($query, $id)
    {
        return $query->where('id', $id);
    }

public function result()
{
    return $this->belongsTo(Result::class);
}

}
