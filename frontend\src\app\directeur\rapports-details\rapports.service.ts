import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class RapportsService {

  constructor(private http: HttpClient) {}
  private apiUrl = 'http://127.0.0.1:8000/api';
   getAllRapports(): Observable<any[]> {
      return this.http.get<any[]>(`${this.apiUrl}/director/rapports`);
    }
    sendRapport(rapportId: number): Observable<any> {
      return this.http.put(`http://127.0.0.1:8000/api/rapport/director/${rapportId}`, {}); // ✅ API call to send the report
    }
    getRapportDetails(rapportId: number): Observable<any> {
      return this.http.get(`${this.apiUrl}/rapports/${rapportId}`);
    }
    updateRapportStatusReject(rapportId: number): Observable<any> {
      return this.http.put(`${this.apiUrl}/rapports/reject/${rapportId}`,{});
    }
    updateRapportStatusValidate(rapportId: number): Observable<any> {
      return this.http.put(`${this.apiUrl}/rapports/validate/${rapportId}`,{});
    }

    sendRapportToClient(demandeId: string): Observable<any> {
      return this.http.put(`${this.apiUrl}/client/rapport/send/${demandeId}`, {});
    }

}
