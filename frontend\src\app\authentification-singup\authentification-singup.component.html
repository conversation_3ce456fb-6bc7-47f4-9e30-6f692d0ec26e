<div class="signup-container">
  <h1 class="title">S'INSCRIRE</h1>

  <!-- Display a global error message if needed -->
  <div *ngIf="errorMessage" class="error-text">
    {{ errorMessage }}
  </div>

  <!-- Loading Spinner Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-popup">
      <div class="spinner"></div>
      <h3>Chargement...</h3>
    </div>
  </div>

  <form [formGroup]="registerForm" (ngSubmit)="registerFormSubmit()">
    <div class="input-group">

      <!-- Name Field -->
      <div class="field-group">
        <label for="name" class="input-label-form">
          Nom:
          <span *ngIf="registerForm.get('name')?.touched && registerForm.get('name')?.errors" class="error">
            <span *ngIf="registerForm.get('name')?.errors?.['required']">
              Le nom est requis.
            </span>
            <span *ngIf="registerForm.get('name')?.errors?.['minlength']">
              Le nom doit contenir au moins 4 caractères.
            </span>
          </span>
        </label>
        <input id="name" type="text" formControlName="name" class="input-field-form">
      </div>

      <!-- Nickname Field -->
      <div class="field-group">
        <label for="nickname" class="input-label-form">
          Prenom:
          <span *ngIf="registerForm.get('nickname')?.touched && registerForm.get('nickname')?.errors" class="error">
            <span *ngIf="registerForm.get('nickname')?.errors?.['required']">
              Le prenom est requis.
            </span>
            <span *ngIf="registerForm.get('nickname')?.errors?.['minlength']">
              Le prenom doit contenir au moins 3 caractères.
            </span>
          </span>
        </label>
        <input id="nickname" type="text" formControlName="nickname" class="input-field-form">
      </div>

      <!-- Email Field -->
      <div class="field-group">
        <label for="email" class="input-label-form">
          Email:
          <span *ngIf="registerForm.get('email')?.touched && registerForm.get('email')?.errors" class="error">
            <span *ngIf="registerForm.get('email')?.errors?.['required']">
              L'email est requis.
            </span>
            <span *ngIf="registerForm.get('email')?.errors?.['pattern']">
              Format d'email invalide.
            </span>
            <span *ngIf="registerForm.get('email')?.errors?.['emailTaken']">
              Cet e-mail existe déjà dans la base de données.
            </span>
          </span>
        </label>
        <input id="email" type="email" formControlName="email" class="input-field-form">
      </div>

      <!-- Address Field -->
      <div class="field-group">
        <label for="adress" class="input-label-form">
          Adresse:
          <span *ngIf="registerForm.get('adress')?.touched && registerForm.get('adress')?.errors" class="error">
            <span *ngIf="registerForm.get('adress')?.errors?.['required']">
              L'adresse est requise.
            </span>
            <span *ngIf="registerForm.get('adress')?.errors?.['minlength']">
              L'adresse doit contenir au moins 10 caractères.
            </span>
          </span>
        </label>
        <input id="adress" type="text" formControlName="adress" class="input-field-form">
      </div>

      <!-- Phone Field (Tunisia) -->
      <div class="field-group">
        <label for="phone" class="input-label-form">
          Téléphone:
          <span *ngIf="registerForm.get('phone')?.touched && registerForm.get('phone')?.errors" class="error">
            <span *ngIf="registerForm.get('phone')?.errors?.['required']">
              Le numéro de téléphone est requis.
            </span>
            <span *ngIf="registerForm.get('phone')?.errors?.['pattern']">
              Numéro de téléphone invalide (Tunisie: 8 chiffres, commençant par 2,3,4,5,7 ou 9).
            </span>
          </span>
        </label>
        <input id="phone" type="text" formControlName="phone" class="input-field-form">
      </div>

      <!-- Fax Field (Optional) -->
      <div class="field-group">
        <label for="fax" class="input-label-form">
          Fax (optionnel):
          <span *ngIf="registerForm.get('fax')?.touched && registerForm.get('fax')?.errors" class="error">
            <span *ngIf="registerForm.get('fax')?.errors?.['pattern']">
              Numéro de fax invalide (Tunisie: 8 chiffres, commençant par 2,3,4,5,7 ou 9).
            </span>
          </span>
        </label>
        <input id="fax" type="text" formControlName="fax" class="input-field-form">
      </div>

      <!-- Password Field -->
      <div class="field-group">
        <label for="password" class="input-label-form">
          Mot de passe:
          <span *ngIf="registerForm.get('password')?.touched && registerForm.get('password')?.errors" class="error">
            <span *ngIf="registerForm.get('password')?.errors?.['required']">
              Le mot de passe est requis.
            </span>
            <span *ngIf="registerForm.get('password')?.errors?.['minlength']">
              Le mot de passe doit contenir au moins 8 caractères.
            </span>
          </span>
        </label>
        <input id="password" type="password" formControlName="password" class="input-field-form">
      </div>

      <!-- Confirm Password Field -->
      <div class="field-group">
        <label for="password_confirmation" class="input-label-form">
          Confirmer Mot de passe:
          <span *ngIf="registerForm.get('password_confirmation')?.touched && registerForm.get('password_confirmation')?.errors" class="error">
            <span *ngIf="registerForm.get('password_confirmation')?.errors?.['required']">
              La confirmation du mot de passe est requise.
            </span>
            <span *ngIf="registerForm.get('password_confirmation')?.errors?.['minlength']">
              La confirmation du mot de passe doit contenir au moins 8 caractères.
            </span>
            <span *ngIf="registerForm.get('password_confirmation')?.errors?.['mismatch']">
              Les mots de passe ne correspondent pas.
            </span>
          </span>
        </label>
        <input id="password_confirmation" type="password" formControlName="password_confirmation" class="input-field-form">
      </div>

    </div>

    <div class="login-link">
      Vous avez un compte ?
      <a (click)="navigateTo('/login')" class="btn-login">Connectez-vous</a>
    </div>

    <button type="submit" class="btn-primary">S'inscrire</button>
  </form>
</div>
