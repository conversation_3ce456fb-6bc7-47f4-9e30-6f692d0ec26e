<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Devis extends Model
{
    use HasFactory;

    protected $table = 'devis';

    protected $fillable = [
        'demande_id',
        'analyse',
        'methode',
        'prix_unitaire',
        'quantite',
        'prix_total',
        'lot'
    ];

    /**
     * Get the demande that owns the devis.
     */
    public function demande()
    {
        return $this->belongsTo(Demande::class, 'demande_id', 'id');
    }
}
