<div class="demandes-container">
  <h2> Fiche de Transmission</h2>

  <!-- Notification d'erreur -->
  <div *ngIf="errorMessage" class="notification error">
    {{ errorMessage }}
  </div>

  <!-- Barre de filtrage -->
  <div class="filter-bar">
    <div class="filter-group">
      <label for="search">Rechercher par numéro ou client:</label>
      <div class="input-with-icon">
        <fa-icon [icon]="faSearch" class="input-icon"></fa-icon>
        <input
          type="text"
          id="search"
          placeholder="Rechercher..."
          [(ngModel)]="searchTerm"
          (input)="onFilterChange()"
          class="filter-input"
        />
      </div>
    </div>
    <div class="filter-group">
      <label for="status">Statut:</label>
      <select id="status" [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="sent">Envoyé</option>
        <option value="not_sent">Non envoyé</option>
      </select>
    </div>
    <div class="filter-group">
      <label for="date">Date:</label>
      <input type="date" id="date" [(ngModel)]="selectedDate" (change)="onFilterChange()" />
    </div>
    <button (click)="clearFilters()" class="btn-clear">
      <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>
      Effacer les filtres
    </button>
  </div>

  <!-- Tableau des fiches -->
  <table>
    <thead>
      <tr>
        <th>ID Fiche</th>
        <th>Demande Numéro</th>
        <th>Nom Client</th>
        <th>Date Transmission</th>
        <th class="action-col">Action</th>
        <th class="status-col">Statut d'envoi</th>
       
      </tr>
    </thead>
    <tbody>
      <!-- Indicateur de chargement -->
      <tr *ngIf="loading" class="loading-row">
        <td colspan="7" class="text-center">
          <div class="spinner-container">
            <div class="spinner"></div>
            <span>Chargement...</span>
          </div>
        </td>
      </tr>

      <!-- Message quand aucune fiche n'est trouvée -->
      <tr *ngIf="!loading && filteredFiches.length === 0" class="empty-row">
        <td colspan="7" class="text-center">
          Aucune fiche de transmission trouvée.
        </td>
      </tr>

      <!-- Affichage des fiches -->
      <ng-container *ngIf="!loading && filteredFiches.length > 0">
        <tr *ngFor="let fiche of filteredFiches | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage }" class="clickable-row">
          <td>{{ fiche.id }}</td>
          <td>{{ fiche.demande.demande_id }}</td>
          <td>{{ fetchUserName(fiche.demande.user_id) }}</td>
          <td>{{ fiche.date_transmission || 'Pas encore envoyé' }}</td>
          <td class="action-col">
            <button class="btn-details" (click)="goToFicheTransmission(fiche.id)">
              <fa-icon [icon]="faEye" style="margin-right: 5px;"></fa-icon>
              Voir détails
            </button>
          </td>
          <td class="status-col">
            <!-- Si non envoyé, on affiche un bouton ; sinon on affiche 'Envoyé' -->
            <button class="btn-action btn-send" *ngIf="!sentStatus[fiche.id]" (click)="sendFiche(fiche.id)">
              <fa-icon [icon]="faPaperPlane" style="margin-right: 5px;"></fa-icon>
              Envoyer
            </button>
            <span class="status sent" *ngIf="sentStatus[fiche.id]">Envoyé</span>
          </td>
         
        </tr>
      </ng-container>
    </tbody>
  </table>

  <!-- Pagination -->
  <pagination-controls
    (pageChange)="currentPage = $event"
    previousLabel="Précédent"
    nextLabel="Suivant"
    class="pagination-custom">
  </pagination-controls>
</div>
