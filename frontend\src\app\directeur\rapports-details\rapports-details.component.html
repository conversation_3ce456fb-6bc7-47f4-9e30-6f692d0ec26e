<div class="rapport-container" *ngIf="reportData">
  <!-- Print But<PERSON> at Top Right -->
  <div class="print-button-container">
    <button class="print-btn" (click)="printRapport()">
      <fa-icon [icon]="faPrint" style="margin-right: 10px;"></fa-icon>Imprimer Rapport
    </button>
  </div>

  <!-- Header -->
  <header class="rapport-header">
    <div class="header-top">

      <div class="status-stamp" *ngIf="reportData.validation !== 2" [ngClass]="{
        'approved-stamp': reportData.validation === 1,
        'rejected-stamp': reportData.validation === 0
      }">
      {{ reportData.validation === 1 ? 'Approuvé' : 'Rejeté' }}

      </div>

      <div class="lab-info">
        <h2>RAPPORT D'ANALYSE N° {{ reportData.rapport_id }}</h2>
        <p>Laboratoire de gestion des analyses d'échantillons de produits aquatiques</p>
      </div>

    </div>
  </header>

  <!-- Infos Client + Laboratoire -->
  <section class="rapport-additional-info">
    <div class="info-left client-info">
      <h3>Client</h3>
      <table>
        <tbody>
          <tr>
            <td class="label"><strong>Nom et Prénom :</strong></td>
            <td class="input-field">{{ clientInfo.name || '⏳ Chargement...' }}   {{clientInfo.nickname }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Email :</strong></td>
            <td class="input-field">{{ clientInfo.email || '--' }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Téléphone :</strong></td>
            <td class="input-field">{{ clientInfo?.phone || '--' }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Adresse :</strong></td>
            <td class="input-field">{{ clientInfo?.adress || '--' }}</td>
          </tr>


        </tbody>
      </table>
    </div>

    <div class="info-right laboratory-info">
      <h3>Laboratoire d'Analyse</h3>
      <table>
        <tbody>
          <tr>
            <td class="label"><strong>Nom :</strong></td>
            <td class="lab-info">{{ labInfo.nom }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Adresse :</strong></td>
            <td class="lab-info">{{ labInfo.adresse }}</td>
          </tr>
          <tr>
            <td class="label"><strong>Contact :</strong></td>
            <td class="lab-info">{{ labInfo.contact }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

  <!-- Description de l’échantillon -->
  <section class="rapport-description">
    <h2>DESCRIPTION DE L'ÉCHANTILLON</h2>
    <table>
      <thead>
        <tr>
          <th>Code échantillon (Client)</th>
          <th>Date de réception</th>
          <th>Nature</th>
          <th>Provenance</th>
          <th>Origine du prélèvement</th>
          <th>Date de prélèvement</th>
          <th>Site</th>
          <th>Nom du préleveur</th>
          <th>Lot</th>
          <th>Référence</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let sample of samples">
          <td>{{ sample.identification_echantillon }}</td>
          <td>{{ sample.reception_date | date: 'yyyy-MM-dd' }}</td>
          <td>{{ sample.nature_echantillon }}</td>
          <td>{{ sample.provenance }}</td>
          <td>{{ sample.origine_prelevement }}</td>
          <td>{{ sample.date_prelevement | date: 'yyyy-MM-dd' }}</td>
          <td>{{ sample.site }}</td>
          <td>{{ sample.nom_preleveur }}</td>
          <td>{{ sample.lot }}</td>
          <td>{{ sample.reference }}</td>
        </tr>
      </tbody>
    </table>
  </section>
  <!-- Description de l’échantillon -->
  <section class="rapport-description">
    <h2>Analyses Demandées</h2>
    <table>
      <thead>
        <tr>
          <th>N°</th>
          <th>Paramètre</th>
          <th>Méthode d’Analyse proposée</th>
          <th>Délai d'Exécution Souhaité</th>
        </tr>
      </thead>
      <tbody>
        <!-- Case 1: When there are unique parameters -->
        <ng-container *ngIf="uniqueParameters.length > 0">
          <tr *ngFor="let param of uniqueParameters; let i = index">
            <td>{{ ('0' + (i + 1)).slice(-2) }}</td>
            <td>{{ param }}</td>
            <td [ngStyle]="{
              'border': (standardMethodAnalyses.length === 0 && i > 0) || (standardMethodAnalyses.length > 0 && i >= standardMethodAnalyses.length) ? 'none' : '',
              'text-align': standardMethodAnalyses.length === 0 && i === 0 ? 'center' : 'center'
            }">
              <ng-container *ngIf="standardMethodAnalyses.length === 0 && i === 0">
                Non spécifié
              </ng-container>
              <ng-container *ngIf="standardMethodAnalyses.length > 0 && i < standardMethodAnalyses.length">
                {{ standardMethodAnalyses[i].parametre }}
              </ng-container>
            </td>
            <td *ngIf="i === 0" [attr.rowspan]="uniqueParameters.length">
              {{ delaiSouhaite || 'Non spécifié' }}
            </td>
          </tr>
        </ng-container>
        <!-- Case 2: When there are no unique parameters -->
        <ng-container *ngIf="uniqueParameters.length === 0">
          <tr>
            <td>01</td>
            <td>Non spécifié</td>
            <td [ngStyle]="{
              'border': standardMethodAnalyses.length === 0 ? 'none' : '',
              'text-align': standardMethodAnalyses.length === 0 ? 'center' : 'center'
            }">
              <ng-container *ngIf="standardMethodAnalyses.length === 0">
                Non spécifié
              </ng-container>
              <ng-container *ngIf="standardMethodAnalyses.length > 0">
                <ng-container *ngFor="let analysis of standardMethodAnalyses; let last = last">
                  {{ analysis.parametre }}<ng-container *ngIf="!last"><br></ng-container>
                </ng-container>
              </ng-container>
            </td>
            <td>{{ delaiSouhaite || 'Non spécifié' }}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </section>
  <!-- Résultats d'analyses -->
  <section class="rapport-resultats">
    <h2>RÉSULTATS D'ANALYSES</h2>
    <form [formGroup]="analysisForm">
      <table>
        <thead>
          <tr>

            <th>Code échantillon</th>
            <th>Paramètre</th>
            <th>Mesurande</th>
            <th>Unité</th>
            <th>Limite d'acceptabilité</th>
            <th>Méthode d'Analyse utilisée</th>
            <th>Date d'Analyse</th>

          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let result of analysisResults; let i = index">
            <!-- Edit / Update Button -->


            <!-- Read-only or Editable Fields -->
            <td>{{ result.code_echantillon }}</td>
            <td>{{ result.parametre }}</td>
            <td><input type="text" [formControlName]="'mesurande_' + i" placeholder="Mesurande"></td>
            <td><input type="text" [formControlName]="'unite_' + i" placeholder="Unité"></td>
            <td><input type="text" [formControlName]="'limite_acceptabilite_' + i" placeholder="Limite Acceptabilité"></td>
            <td>{{ result.methode_analyse_utilisee }}</td>
            <td><input type="date" [formControlName]="'date_analyse_' + i"></td>

          </tr>
        </tbody>
      </table>
    </form>
  </section>

  <div class="action-buttons" [hidden]="hideButtons">
    <!-- Show validate button only if the report is NOT already validated -->
    <button class="validate-btn" *ngIf="reportData.validation !== 1" (click)="validateRapport()">
      <fa-icon [icon]="faCheck" style="margin-right: 10px;"></fa-icon>Valider
    </button>

    <!-- Show reject button only if the report is NOT already rejected -->
    <button class="reject-btn" *ngIf="reportData.validation !== 0" (click)="rejectRapport()">
      <fa-icon [icon]="faTimes" style="margin-right: 10px;"></fa-icon>Rejeter
    </button>
  </div>

  <!-- Confirmation Modal -->
  <div class="modal-overlay" *ngIf="showConfirmModal" (click)="cancelAction()"></div>
  <div class="modal-container" *ngIf="showConfirmModal">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <h3>Confirmation</h3>
      <p *ngIf="confirmAction === 'validate'">Êtes-vous sûr de vouloir valider ce rapport?</p>
      <p *ngIf="confirmAction === 'reject'">Êtes-vous sûr de vouloir rejeter ce rapport?</p>
      <div class="modal-buttons">
        <button class="cancel-btn" (click)="cancelAction()">Annuler</button>
        <button
          [ngClass]="{'validate-btn': confirmAction === 'validate', 'reject-btn': confirmAction === 'reject'}"
          (click)="confirmActionExecution()">
          OK
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Modal -->
  <div class="modal-overlay" *ngIf="showLoadingModal"></div>
  <div class="modal-container" *ngIf="showLoadingModal">
    <div class="modal-content loading-content">
      <fa-icon [icon]="faSpinner" [spin]="true" [size]="'3x'"></fa-icon>
      <p>Traitement en cours...</p>
    </div>
  </div>
</div>
