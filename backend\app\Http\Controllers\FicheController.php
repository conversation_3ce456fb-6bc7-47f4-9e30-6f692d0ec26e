<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FicheTransmission;
use App\Models\Fiche;
use App\Models\Demande;
use App\Models\User;
use App\Models\Rapport;
use App\Models\Sample;
use Carbon\Carbon;
use App\Models\Notification;
class FicheController extends Controller
{
    // List all Fiches de Transmission
    public function index()
    {
        $fiches = FicheTransmission::with('demande')->get(); // ✅ Use singular "demande"
        return response()->json($fiches);
    }
    public function getAnalystFiches()
    {
        $fiches = FicheTransmission::with('demande')->
        where('status','sented')->get(); // ✅ Use singular "demande"
        return response()->json($fiches);
    }
// ✅ Add this method inside FicheController
private function calculateResultsDate(Carbon $startDate, int $days)
{
    $currentDate = clone $startDate;
    $count = 0;

    while ($count < $days) {
        $currentDate->addDay();
        if (!$currentDate->isWeekend()) { // Exclude Saturdays and Sundays
            $count++;
        }
    }

    return $currentDate->format('Y-m-d');
}

    // Create Fiches de Transmission and Fiches for a specific Demande
    public function createFiches($demande_id)
    {
        // ✅ Fetch the demande record
        $demande = Demande::where('demande_id', $demande_id)->first();
        
        if (!$demande) {
            return response()->json(['message' => 'Demande not found'], 404);
        }
        
        // ✅ Fetch the user
        $client = User::where('id', $demande->user_id)->first();
        
        if (!$client) {
            return response()->json(['message' => 'Client not found'], 404);
        }
        
        $clientName = $client->name;
        $clientNickname = $client->nickname;
        $dateTransmission = now()->format('Y-m-d');
        
        // ✅ Create Fiche de Transmission
        $ficheTransmission = FicheTransmission::create([
            'demande_id' => $demande_id,
            'client_name' => $clientName,
            'client_nickname' => $clientNickname,
            'date_transmission' => null, // ✅ Initially NULL
        ]);
        
        if (!$demande->samples()->exists()) {
            return response()->json(['message' => 'No samples found for this demande'], 400);
        }
        
        // ✅ Generate Fiches for each sample
        foreach ($demande->samples as $index => $sample) {
            // ✅ Get the sequential number for the sample in the current demande
            $sampleIndex = str_pad(($index + 1), 4, '0', STR_PAD_LEFT); // 0001, 0002, 0003...
        
            // ✅ Get the Julian day of the year
            $dayOfYear = str_pad(now()->format('z') + 1, 3, '0', STR_PAD_LEFT);
        
            // ✅ Get the last two digits of the year
            $yearTwoDigits = now()->format('y');
        
            // ✅ Final formatted code laboratoire
            $codeLaboratoire = "{$sampleIndex}-{$dayOfYear}-{$yearTwoDigits}";
        
            // ✅ Calculate result date (5 business days)
            $dateRemise = $this->calculateResultsDate(Carbon::parse($dateTransmission), 5);
        
            // ✅ Merge analyses_demandees with analyse_souhaite
            $allAnalyses = is_array($sample->analyses_demandees) ? $sample->analyses_demandees : [];
            if (!empty($sample->analyse_souhaite) && !in_array($sample->analyse_souhaite, $allAnalyses)) {
                $allAnalyses[] = $sample->analyse_souhaite;
            }
        
            // ✅ Create the fiche
            Fiche::create([
                'sample_id' => $sample->id,
                'fiche_transmission_id' => $ficheTransmission->id,
                'code_laboratoire' => $codeLaboratoire,
                'nature_echantillon' => $sample->nature_echantillon,
                'masse_echantillon' => $sample->masse_echantillon,
                'date_transmission' => null, // ✅ Initially NULL
                'date_remise_resultats' => $dateRemise,
                'analyses_demandees' => $allAnalyses,
                'observations' => 'RAS',
            ]);
        }
        
        $demande->fiche_id = $ficheTransmission->id;
        $demande->save();
        return response()->json(['message' => 'Fiches de transmission created successfully']);
    }
    public function sendFiche($id)
    {
        // Find the FicheTransmission record
        $ficheTransmission = FicheTransmission::find($id);
    
        if (!$ficheTransmission) {
            return response()->json(['message' => 'FicheTransmission not found'], 404);
        }
    
        // Ensure the related demande exists
        $demande = Demande::where('demande_id', $ficheTransmission->demande_id)->first();
        if (!$demande) {
            return response()->json(['message' => 'Demande not found'], 404);
        }
    
        // ✅ Set transmission date to now
        $currentDate = Carbon::now();
    
        // ✅ Update status & date_transmission in FicheTransmission
        $ficheTransmission->status = 'sented';
        $ficheTransmission->date_transmission = $currentDate;
        $ficheTransmission->save();
    
        // ✅ Update date_transmission in all related fiches
        Fiche::where('fiche_transmission_id', $ficheTransmission->id)
            ->update(['date_transmission' => $currentDate]);
    
        // ✅ Create a notification
        Notification::create([
            'user_id' => $demande->user_id, // Get user_id from Demande
            'title' => 'Fiche de transmission',
            'message' => 'Fiche de transmission pour la demande de numéro '.$demande->demande_id.'.',
            'type' => 'fiche',
            'demande_id' => $demande->id, // Correct demande_id
            'fiche_id' => $ficheTransmission->id // Correct fiche_id
        ]);
    
        return response()->json(['message' => 'Fiche sent successfully']);
    }
    // Show all Fiches for a specific Fiche de Transmission
    public function show($fiche_transmission_id)
    {
        $fiches = Fiche::where('fiche_transmission_id', $fiche_transmission_id)->get();
        $demande = Demande::where('fiche_id', $fiche_transmission_id)->get();
$rapport = Rapport::where('demande_id', $demande->id)->get();

        return response()->json($fiches);
    }
}
