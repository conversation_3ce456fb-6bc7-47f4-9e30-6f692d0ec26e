/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* Container Styles */
.fiche-details-container {
  font-family: 'Montserrat', sans-serif;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 1rem auto;
  max-width: 1200px;
}

/* 2. HEADER */
.fiches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.fiches-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-bottom: 4px solid #2496d3;
  padding-bottom: 0.25rem;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, #000, #888);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* Style for the icon inside the title to make it visible */
.fiches-title fa-icon {
  color: #000000; /* Blue color to match the border */

}

.back-btn, .print-btn {
  color: #fff;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  transition: transform 0.3s, box-shadow 0.3s;
  font-weight: bold;
}

.back-btn {
  background: #007bff;
}

.print-btn {
  background: #28a745;
}

.back-btn:hover, .print-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.print-btn:hover {
  background: #218838;
}

.back-btn:hover {
  background: #0069d9;
}

/* Glow animation */
@keyframes glowText {
  from { text-shadow: 0 0 8px rgba(36,150,211,0.4); }
  to   { text-shadow: 0 0 16px rgba(36,150,211,0.8); }
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

thead {
  background: linear-gradient(to right, #2496d3, #0a6aa1);
  color: white;
}

th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

tbody tr {
  background-color: white;
  transition: background-color 0.3s;
}

tbody tr:hover {
  background-color: #f1f3f5;
}

tbody tr:last-child td {
  border-bottom: none;
}

/* List Styles */
ul {
  margin: 0;
  padding-left: 1.2rem;
}

li {
  margin-bottom: 0.3rem;
}

li:last-child {
  margin-bottom: 0;
}

/* Message Styles */
.loading-message, .error, .no-data {
  text-align: center;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.loading-message {
  background-color: #e9f5fe;
  color: #2496d3;
}

.error {
  background-color: #ffe9e9;
  color: #dc3545;
}

.no-data {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .fiche-details-container {
    padding: 1.5rem;
  }

  .fiches-header {
    flex-direction: column;
    gap: 1rem;
  }

  table {
    display: block;
    overflow-x: auto;
  }

  th, td {
    padding: 0.8rem;
  }
}
