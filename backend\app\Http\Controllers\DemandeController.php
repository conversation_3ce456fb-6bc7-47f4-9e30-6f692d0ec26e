<?php

namespace App\Http\Controllers;

use App\Events\NewDemandeNotification;
use App\Models\Demande;
use App\Models\Sample;
use App\Models\Facture;
use App\Notifications\DemandeNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Notification;
use NunoMaduro\Collision\Exceptions\TestException;
use App\Events\DemandeUpdated;
use App\Models\Derogation;
use App\Models\User;
use Carbon\Carbon;
    use App\Models\Devis;
use Pusher\Pusher;
use Illuminate\Support\Facades\Mail;
use App\Mail\DemandeNotificationEmail;
class DemandeController extends Controller
{
    public function getUserById($id)
{
    try {
        // Fetch user with related demandes
        $user = User::find($id);

        // Check if user exists
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not found.'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $user
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error retrieving user data.',
            'error' => $e->getMessage()
        ], 500);
    }
}
  function getAnalysisMethod($analysisName)
{
    $methods = [
        'Biotoxine (DSP)' => 'NF EN 16024: 2013 Par LC-MS/MS',
        'Biotoxine (PSP)' => 'Méthode interne validée Par LC-MS/MS',
        'Biotoxine (ASP)' => 'Accréditée selon la norme ISO/CEI 17025:2017',
        'Lipide' => 'Méthode interne validée extraction gravimétrique',
        'Acide gras' => 'ISO 12966-2:2012 / ISO 12966-4:2015 (CPG)',
        'Humidité' => 'NFV04-401',
        'Cendres' => 'NFV04-404',
        'Protéine' => 'Méthode interne validée Spectrophotométrie',
    ];


    return $methods[$analysisName] ?? 'Method standard';
}
public function store(Request $request)
{
    // ✅ Step 1: Validate request data
    $request->validate([
        'mode_reglement' => 'required|string',
        'enregistrements' => 'required|array|min:1',
        'delai_souhaite' => 'nullable|string',
        'enregistrements.*.reference' => 'required|string',
        'enregistrements.*.delai_souhaite' => 'nullable|string',
        'enregistrements.*.analyse_souhaite' => 'nullable|string',
        'enregistrements.*.date_prelevement' => 'nullable|date',
        'enregistrements.*.origine_prelevement' => 'nullable|string',
        'enregistrements.*.nom_preleveur' => 'nullable|string',
        'enregistrements.*.site' => 'nullable|string',
        'enregistrements.*.nature_echantillon' => 'required|string',
        'enregistrements.*.provenance' => 'required|string',
        'enregistrements.*.masse_echantillon' => 'required|numeric',
        'enregistrements.*.etat' => 'required|string',
        'enregistrements.*.analyses_demandees' => 'required|array',
        'enregistrements.*.lot' => 'required|string',
    ]);

    // ✅ Step 2: Get authenticated user
    $userId = Auth::id();

    if (!$userId) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    // ✅ Step 3: Generate unique demande_id
    $today = now()->format('Y-m-d');
    $totalEnregistrementsToday = Demande::where('demande_date', $today)->count();
    $dayOfYear = now()->format('z') + 1;
    $newTotal = $totalEnregistrementsToday + 1;
    $demandeId = str_pad($newTotal, 4, '0', STR_PAD_LEFT) . '-' . str_pad($dayOfYear, 3, '0', STR_PAD_LEFT);

    while (Demande::where('demande_id', $demandeId)->exists()) {
        $newTotal++;
        $demandeId = str_pad($newTotal, 4, '0', STR_PAD_LEFT) . '-' . str_pad($dayOfYear, 3, '0', STR_PAD_LEFT);
    }

    // ✅ Step 4: Define available analyses with accreditation status
    $analysesDisponibles = [
        ['name' => 'Biotoxine (DSP)', 'price' => 350, 'accredited' => true],
        ['name' => 'Biotoxine (PSP)', 'price' => 350, 'accredited' => true],
        ['name' => 'Biotoxine (ASP)', 'price' => 400, 'accredited' => true],
        ['name' => 'Lipide', 'price' => 60, 'accredited' => false],
        ['name' => 'Acide gras', 'price' => 80, 'accredited' => false],
        ['name' => 'Humidité', 'price' => 20, 'accredited' => true],
        ['name' => 'Cendres', 'price' => 25, 'accredited' => true],
        ['name' => 'Protéine', 'price' => 45, 'accredited' => true],
    ];

    // ✅ Step 5: Determine if there are non-accredited analyses
    $AccreditedNames = [];
    foreach ($analysesDisponibles as $analysis) {
        if ($analysis['accredited']) {
            $AccreditedNames[] = $analysis['name'];
        }
    }

    $hasAccredited = 0;
    foreach ($request->enregistrements as $enregistrementData) {
        $analysesForDevis = $enregistrementData['analyses_demandees'] ?? [];
        if (!empty($enregistrementData['analyse_souhaite']) && !in_array($enregistrementData['analyse_souhaite'], $analysesForDevis)) {
            $analysesForDevis[] = $enregistrementData['analyse_souhaite'];
        }

        foreach ($analysesForDevis as $analysis) {
            if (in_array($analysis, $AccreditedNames)) {
                $hasAccredited = 1;
                break 2; // Exit both loops as soon as a non-accredited analysis is found
            }
        }
    }

    // ✅ Step 6: Create demande with the new field
    $demande = Demande::create([
        'demande_id' => $demandeId,
        'total_enregistrements' => count($request->enregistrements),
        'demande_date' => $today,
        'user_id' => $userId,
        'mode_reglement' => $request->mode_reglement,
        'delai_souhaite' => $request->delai_souhaite,
        'status' => 'pending',
        'analyses_accredite' => $hasAccredited,
    ]);

    // ✅ Step 7: Notify admin about the Nouvelle Demande
    $notification=Notification::create([
        'user_id' => $userId,
        'title' => 'Nouvelle Demande',
        'message' => 'Une nouvelle demande a été soumise. Veuillez la consulter.',
        'type' => 'demande',
        'demande_id' => $demande->id,
        'demande' => $demandeId
    ]);
    $user = User::find($userId);
    $demande = Demande::find($demande->id);
    $receptionists=User::where('role', 'receptionist')->first();
    // Send the email
    Mail::to($receptionists->email)->send(new DemandeNotificationEmail($notification, $demande, $user));
    broadcast(new NewDemandeNotification($demande))->toOthers();

    // ✅ Step 8: Prepare analysis prices
    $analysisPrices = [];
    foreach ($analysesDisponibles as $analysis) {
        $analysisPrices[$analysis['name']] = $analysis['price'];
    }

    // ✅ Step 9: Create samples & calculate total amount for Devis Proforma
    $totalAmount = 0;
    $devisData = [];
    $sampleNumber = 1;

    foreach ($request->enregistrements as $enregistrementData) {
        $identificationEchantillon = str_pad($sampleNumber, 4, '0', STR_PAD_LEFT) . '-' . str_pad($dayOfYear, 3, '0', STR_PAD_LEFT);

        $sample = Sample::create([
            'demande_id' => $demande->id,
            'identification_echantillon' => $identificationEchantillon,
            'reference' => $enregistrementData['reference'] ?? null,
            'delai_souhaite' => $enregistrementData['delai_souhaite'] ?? null,
            'analyse_souhaite' => $enregistrementData['analyse_souhaite'] ?? null,
            'nature_echantillon' => $enregistrementData['nature_echantillon'] ?? null,
            'provenance' => $enregistrementData['provenance'] ?? null,
            'masse_echantillon' => $enregistrementData['masse_echantillon'] ?? null,
            'etat' => $enregistrementData['etat'] ?? null,
            'analyses_demandees' => $enregistrementData['analyses_demandees'] ?? [],
            'lot' => $enregistrementData['lot'] ?? null,
            'date_prelevement' => $enregistrementData['date_prelevement'] ?? null,
            'origine_prelevement' => $enregistrementData['origine_prelevement'] ?? null,
            'site' => $enregistrementData['site'] ?? null,
            'nom_preleveur' => $enregistrementData['nom_preleveur'] ?? null,
        ]);

        $analysesForDevis = $enregistrementData['analyses_demandees'] ?? [];
        if (!empty($enregistrementData['analyse_souhaite']) && !in_array($enregistrementData['analyse_souhaite'], $analysesForDevis)) {
            $analysesForDevis[] = $enregistrementData['analyse_souhaite'];
        }

        foreach ($analysesForDevis as $analysis) {
            $unitPrice = $analysisPrices[$analysis] ?? 0;
            $quantity = 1;
            $totalPrice = $unitPrice * $quantity;
            $totalAmount += $totalPrice;

            $devisData[] = [
                'demande_id' => $demande->id,
                'analyse' => $analysis,
                'methode' => $this->getAnalysisMethod($analysis),
                'prix_unitaire' => $unitPrice,
                'quantite' => $quantity,
                'prix_total' => $totalPrice,
                'lot' => $enregistrementData['lot'] ?? null,
            ];
        }

        $sampleNumber++;
    }

    // ✅ Step 10: Store Devis Proforma
    foreach ($devisData as $devisItem) {
        Devis::create($devisItem);
    }

    return response()->json([
        'message' => 'Demande created successfully',
        'demande_id' => $demande->demande_id,
        'total_amount' => $totalAmount,
    ]);
}
public function create($demandeId)
    {
        // Find the demande by demande_id (the string identifier)
        $demande = Demande::where('demande_id', $demandeId)->firstOrFail();

        // Check if a facture already exists
        if ($demande->facture_id) {
            return response()->json([
                'message' => 'Facture already exists for this demande.'
            ], 400);
        }

        // Calculate the total amount from all devis
        $totalAmount = Devis::where('demande_id', $demande->id)->sum('prix_total');

        // Create a new facture
        $facture = Facture::create([
            'total_amount' => $totalAmount,
            'facture_date' => now()->toDateString(),
            'status' => 'en attente de paiment',
            'demande' => $demandeId
        ]);

        // Link the facture to the demande
        $demande->facture_id = $facture->id;
        $demande->save();

        // Retrieve all devis for the response
        $devis = Devis::where('demande_id', $demande->id)->get();

        // Return the response
        return response()->json([
            'message' => 'Facture created successfully.',
            'facture' => $facture,
            'devis' => $devis,
        ], 201);
    }
public function updateAnalysePrice(Request $request, $demandeId, $analyse)
{
    // Validate the incoming request to ensure prix_unitaire is provided and valid
    $request->validate([
        'prix_unitaire' => 'required|numeric|min:0',
    ]);

    // Find the demande first to get the internal ID
    $demande = Demande::where('demande_id', $demandeId)->first();

    if (!$demande) {
        return response()->json([
            'message' => 'Demande not found',
        ], 404);
    }

    // Find all Devis entries matching the demande_id and analyse
    $devis = Devis::where('demande_id', $demande->id)
                  ->where('analyse', $analyse)
                  ->get();

    // Check if any Devis entries exist
    if ($devis->isEmpty()) { // Use isEmpty() for Collections instead of !$devis
        return response()->json([
            'message' => 'Devis entry not found for the specified demande_id and analyse',
        ], 404);
    }

    // Update each Devis entry in the Collection
    foreach ($devis as $devisItem) {
        $devisItem->prix_unitaire = $request->prix_unitaire;
        $devisItem->prix_total = $devisItem->prix_unitaire * $devisItem->quantite;
        $devisItem->save();
    }

    // Return a success response with the updated Devis details
    return response()->json([
        'message' => 'Price updated successfully',
        'devis' => $devis,
    ], 200);
}

    // public function store(Request $request)
    // {
    //     // Validate request data
    //     $request->validate([
    //         'enregistrements' => 'required|array|min:1',
    //         'enregistrements.*.identification_echantillon' => 'required|string',
    //         'enregistrements.*.reference'=> 'required|string',
    //         'enregistrements.*.delai_souhaite'=> 'nullable|string',
    //         'enregistrements.*.analyse_souhaite'=> 'nullable|string',
    //         'enregistrements.*.nature_echantillon' => 'required|string',
    //         'enregistrements.*.provenance' => 'required|string',
    //         'enregistrements.*.masse_echantillon' => 'required|numeric',
    //         'enregistrements.*.etat' => 'required|string',
    //         'enregistrements.*.analyses_demandees' => 'required|array',
    //         'enregistrements.*.lot' => 'required|string',
    //         'enregistrements.*.mode_reglement' => 'required|string',
    //     ]);

    //     // Get authenticated user
    //     $userId = auth('sanctum')->user()->id;

    //     if (!$userId) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     // Get today's date
    //     $today = now()->format('Y-m-d');

    //     // Get the total enregistrements created today (resets daily)
    //     $totalEnregistrementsToday = Demande::where('demande_date', $today)->sum('total_enregistrements');

    //     // Get the day of the year (001 to 365)
    //     $dayOfYear = now()->format('z') + 1; // 'z' is zero-based, so add 1

    //     // Compute the demande_id (reset count daily)
    //     $newTotal = $totalEnregistrementsToday + count($request->enregistrements);
    //     $demandeId = str_pad($newTotal, 4, '0', STR_PAD_LEFT) . '-' . str_pad($dayOfYear, 3, '0', STR_PAD_LEFT);

    //     // Create demande
    //     $demande = Demande::create([
    //         'demande_id' => $demandeId,
    //         'total_enregistrements' => count($request->enregistrements),
    //         'demande_date' => $today,
    //         'user_id' => $userId,
    //          'status' => 'pending'
    //     ]);
    //     Notification::create([
    //         'user_id' => auth('sanctum')->user()->id,
    //         'title' => 'Nouvelle Demande',
    //         'message' => 'A Nouvelle Demande has been submitted. Please review it.',
    //         'type' => 'demande',
    //         'demande_id' => $demande->id,
    //     ]);
    //     broadcast(new NewDemandeNotification($demande))->toOthers();

    //     // Create enregistrements
    //     foreach ($request->enregistrements as $enregistrementData) {
    //         $enregistrementData['demande_id'] = $demande->id;
    //         Sample::create($enregistrementData);
    //     }

    //     return response()->json([
    //         'message' => 'Demande created successfully',
    //         'demande_id' => $demande->demande_id,
    //     ]);
    // }

    public function getUserDemandes()
{
    // Get authenticated user
    $user = auth('sanctum')->user();

    if (!$user) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    // Retrieve all demandes associated with the logged-in user
    $demandes = Demande::where('user_id', $user->id)->with('samples')->get();

    return response()->json([
        'message' => 'Demandes retrieved successfully',
        'demandes' => $demandes
    ]);
}
public function getAllDevisForDemande($demandeId)
    {
        // Find the demande by its custom demande_id
        $demande = Demande::with('devis')->where('demande_id', $demandeId)->first();

        // Check if the demande exists
        if (!$demande) {
            return response()->json([
                'message' => 'Demande not found',
            ], 404);
        }

        // Return the demande with all related devis
        return response()->json([
            'message' => 'All devis for demande retrieved successfully',
            'demande' => [
                'id' => $demande->id,
                'demande_id' => $demande->demande_id,
                'user_id' => $demande->user_id,
                'mode_reglement' => $demande->mode_reglement,
                'total_enregistrements' => $demande->total_enregistrements,
                'demande_date' => $demande->demande_date,
                'status' => $demande->status,
                'created_at' => $demande->created_at,
                'updated_at' => $demande->updated_at,
                'devis' => $demande->devis->map(function ($devis) {
                    return [
                        'id' => $devis->id,
                        'demande_id' => $devis->demande_id,
                        'analyse' => $devis->analyse,
                        'methode' => $devis->methode,
                        'prix_unitaire' => $devis->prix_unitaire,
                        'quantite' => $devis->quantite,
                        'prix_total' => $devis->prix_total,
                        'created_at' => $devis->created_at,
                        'updated_at' => $devis->updated_at,
                    ];
                })->all(),
            ],
        ], 200);
    }
public function getAllDemandes()
{
    try {
        // Fetch all demandes with their related samples
        $demandes = Demande::with('samples')
            ->where('status' ,'valid')->get();

        return response()->json([
            'success' => true,
            'data' => $demandes
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch demandes',
            'error' => $e->getMessage()
        ], 500);
    }
}
public function getAllDemande()
{
    try {
        // Fetch all demandes with their related samples, excluding those with status 'valid'
        $demandes = Demande::with('samples')
            ->where('status', '!=', 'valid')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $demandes
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch demandes',
            'error' => $e->getMessage()
        ], 500);
    }
}
public function validDemandes()
{
    try {
        // Fetch all demandes with their related samples
        $demandes = Demande::with('samples')
            ->where('status' ,'valid')->get();

        return response()->json([
            'success' => true,
            'data' => $demandes
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to fetch demandes',
            'error' => $e->getMessage()
        ], 500);
    }
}

public function getAllDerogations()
{

        $demandes = Demande::where('status','derogation')->get();
            return response()->json([

                'data' => $demandes
            ], 200);

    $derogations = Derogation::get();
        return response()->json([

            'data' => $derogations
        ], 200);
}

public function getDerogatedNotifications()
{


        $notifications = Notification::where('title', 'Demande de validation avec derogation')->get();

        return response()->json([
            'notifications' => $notifications,
        ]);

}
public function updateStatus(Request $request, Demande $demande)
{
    $request->validate(['status' => 'required|string']);

    // Update demande status
    $demande->update(['status' => $request->status]);

    // Define notification details
    $title = '';
    $message = '';
    $recipientUsers = [];

    switch ($request->status) {
        case 'ongoing_validation':
            $title = 'Demande Echantillon';
            $message = 'Your demande is now under validation. Bring your samples to the receptionist.';
            $recipientUsers = [$demande->user]; // Client gets notified
            break;
        case 'valid':
            $title = 'Demande Validated';
            $message = 'Your demande is validated. Check your proforma invoice.';
            $recipientUsers = [$demande->user, $demande->receptionist]; // Client + Receptionist
            break;
        case 'waiting_validation':
            $title = 'Derogation Approval Needed';
            $message = 'A derogation request has been submitted. Please review and validate.';
            $recipientUsers = [$demande->director]; // Only Director gets notified
            break;
        case 'rejected':
            $title = 'Demande Rejected';
            $message = 'Your demande has been rejected. Check the reason provided.';
            $recipientUsers = [$demande->user, $demande->receptionist]; // Client + Receptionist
            break;
    }

    // Send notifications to the appropriate users
    Notification::send($recipientUsers, new DemandeNotification($demande, $title, $message));

    // Broadcast the update for frontend clients listening to the channel


    return response()->json(['message' => 'Status updated and notification sent successfully']);
}
public function getDemandeInformation($demande_id)
{
    // Retrieve the Demande by the provided demande_id
    $demande = Demande::with('samples', 'user')
                  ->select('demande_id', 'demande_date', 'user_id', 'mode_reglement')
                  ->where('demande_id', $demande_id)
                  ->first();


    // Check if the Demande exists
    if (!$demande) {
        return response()->json(['message' => 'Demande not found.'], 404);
    }

    // Return the Demande data with its associated Samples and User
    return response()->json([
        'demande' => $demande,
        'samples' => $demande->samples, // All samples related to this demande
        'user' => $demande->user, // The user associated with the demande
    ]);
}
public function getDemandeDetails($demande_id)
{
    // Retrieve the Demande based on demande_id
    $demande = Demande::with('samples') // Eager load related samples
                      ->where('demande_id', $demande_id)
                      ->first();

    // Check if the Demande exists
    if (!$demande) {
        return response()->json(['message' => 'Demande not found.'], 404);
    }


    // Prepare the data to return
    $demandeData = [
        'demande_id' => $demande->demande_id,
        'demande_date' => $demande->demande_date,
        'user_name' => $demande->user->name,
        'user_nickname' => $demande->user->nickname,
        'user_id'=> $demande->user->id,// Assuming the Demande belongs to a User
        'user_email' => $demande->user->email,
        'mode_reglement' => $demande->mode_reglement,
        'status'=> $demande->status, // Assuming you want the email as well
        'samples' => $demande->samples->map(function($sample) {
            return [
                'identification_echantillon' => $sample->identification_echantillon,
                'reference' => $sample->reference,
                'nature_echantillon' => $sample->nature_echantillon,
                'provenance' => $sample->provenance,
                'masse_echantillon' => $sample->masse_echantillon,
                'etat' => $sample->etat,
                'date_prelevement' => $sample->date_prelevement,
                'origine_prelevement' => $sample->origine_prelevement,
                'site' => $sample->site,
                'nom_preleveur' => $sample->nom_preleveur,
                'analyses_demandees' => $sample->analyses_demandees, // Assuming this is a JSON field
                'delai_souhaite' => $sample->delai_souhaite,
                'analyse_souhaite' => $sample->analyse_souhaite,
                'lot' => $sample->lot,

            ];
        }),
    ];

    // Return the Demande data along with its samples
    return response()->json([
        'demande' => $demandeData
    ]);
}
public function getDemandeBy($identifier)
{
    // Determine if the identifier is numeric (id) or a string (demande_id)
    if (is_numeric($identifier)) {
        $demande = Demande::with('samples')  // Eager load related samples
                          ->byId($identifier)
                          ->first();
    } else {
        $demande = Demande::with('samples')  // Eager load related samples
                          ->byDemandeId($identifier)
                          ->first();
    }

    // Check if the Demande exists
    if (!$demande) {
        return response()->json(['message' => 'Demande not found.'], 404);
    }
    $userdetails = User::find($demande->user_id);


    // Prepare the data to return
    $demandeData = [
        'demande_id' => $demande->demande_id,
        'demande_date' => $demande->demande_date,
        'user_name' => $demande->user->name, // Assuming the Demande belongs to a User
        'user_nickname' => $demande->user->nickname,
        'user_email' => $demande->user->email,
        'user_id'=> $demande->user->id,
        'mode_reglement' => $demande->mode_reglement,
        'status' => $demande->status,
        'devis_sent'=>$demande->devis_sent,
        'facture_id'=>$demande->facture_id,
        'delai_souhaite'=>$demande->delai_souhaite,
        'rapport_created'=>$demande->rapport_created,
        'analyses_accredite'=>$demande->analyses_accredite,
        'validation'=>$demande->validation,
        'result_id'=>$demande->result_id,
        'payment_id'=>$demande->payment_id,
        'telephone'=>$userdetails->phone,
        'adresse'=>$userdetails->adress,
        'fax'=>$userdetails->fax,
        'samples' => $demande->samples->map(function ($sample) {
            return [
                'identification_echantillon' => $sample->identification_echantillon,
                'reference' => $sample->reference,
                'nature_echantillon' => $sample->nature_echantillon,
                'provenance' => $sample->provenance,
                'masse_echantillon' => $sample->masse_echantillon,
                'etat' => $sample->etat,
                'analyses_demandees' => $sample->analyses_demandees, // Assuming this is a JSON field
                'delai_souhaite' => $sample->delai_souhaite,
                'analyse_souhaite' => $sample->analyse_souhaite,
                'lot' => $sample->lot,
                'date_prelevement' => $sample->date_prelevement,
                'origine_prelevement' => $sample->origine_prelevement,
                'site' => $sample->site,
                'nom_preleveur' => $sample->nom_preleveur,
            ];
        }),
    ];

    // Return the Demande data along with its samples
    return response()->json([
        'demande' => $demandeData
    ]);
}
public function getDerogationDetails(Request $request, $identifier)
{
    // Fetch the demande to ensure it exists
    if (is_numeric($identifier)) {
        $demande = Demande::with('samples')  // Eager load related samples
                          ->byId($identifier)
                          ->first();
    } else {
        $demande = Demande::with('samples')  // Eager load related samples
                          ->byDemandeId($identifier)
                          ->first();
    }

    // Retrieve all derogations associated with the demande_id
    $derogations = Derogation::where('demande_id', $demande->id)
        ->with(['demande.user']) // Eager load demande and user relationship for demander name
        ->get();

    if ($derogations->isEmpty()) {
        return response()->json([
            'message' => 'Aucune dérogation trouvée pour cette demande.',
        ], 404);
    }

    // Transform the data for a clean response
    $derogationData = $derogations->map(function ($derogation) {
        return [
            'id' => $derogation->id,
            'demande_id' => $derogation->demande_id,
            'identification_echantillon' => $derogation->identification_echantillon,
            'nature_echantillon' => $derogation->nature_echantillon,
            'reference' => $derogation->reference,
            'provenance' => $derogation->provenance,
            'demandeur' => $derogation->demande->user->name ?? 'N/A',
            'date_demande_client' => $derogation->date_demande_client,
            'masse_echantillon' => $derogation->masse_echantillon,
            'analyses_demandees' => $derogation->analyses_demandees,
            'description_ecart' => $derogation->description_ecart,
            'reponse_du_client' => $derogation->reponse_du_client,
            'decision' => $derogation->decision,
            'date_et_visa_du_DL' => $derogation->date_et_visa_du_DL,
            'date_et_visa_du_demander' => $derogation->date_et_visa_du_demander,
            'created_at' => $derogation->created_at->toDateTimeString(),
            'updated_at' => $derogation->updated_at->toDateTimeString(),
        ];
    });

    return response()->json([
        'message' => 'Informations sur les dérogations récupérées avec succès.',
        'demande_id' => $demande->demande_id,
        'derogations' => $derogationData,
    ], 200);
}
}

