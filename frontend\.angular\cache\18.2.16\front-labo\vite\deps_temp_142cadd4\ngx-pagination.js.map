{"version": 3, "sources": ["../../../../../../node_modules/ngx-pagination/fesm2020/ngx-pagination.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nfunction PaginationControlsComponent_ul_3_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.previous());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_1_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isFirstPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", 1 < p_r3.getCurrent());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isFirstPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    })(\"click\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const page_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.setCurrent(page_r6.value));\n    });\n    i0.ɵɵelementStart(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderPageLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(5, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 16)(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.screenReaderCurrentLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r6.label === \"...\" ? page_r6.label : i0.ɵɵpipeBind2(6, 2, page_r6.label, \"\"));\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_4_a_1_Template, 6, 5, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_4_ng_container_2_Template, 7, 5, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r6 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"current\", p_r3.getCurrent() === page_r6.value)(\"ellipsis\", page_r6.label === \"...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() !== page_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.getCurrent() === page_r6.value);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    })(\"click\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext(3);\n      const p_r3 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(p_r3.next());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.screenReaderPageLabel);\n  }\n}\nfunction PaginationControlsComponent_ul_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_5_a_1_Template, 4, 2, \"a\", 10)(2, PaginationControlsComponent_ul_3_li_5_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"disabled\", p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !p_r3.isLastPage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", p_r3.isLastPage());\n  }\n}\nfunction PaginationControlsComponent_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 4);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_Template, 3, 4, \"li\", 5);\n    i0.ɵɵelementStart(2, \"li\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PaginationControlsComponent_ul_3_li_4_Template, 3, 6, \"li\", 7)(5, PaginationControlsComponent_ul_3_li_5_Template, 3, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const p_r3 = i0.ɵɵreference(1);\n    i0.ɵɵclassProp(\"responsive\", ctx_r3.responsive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", p_r3.getCurrent(), \" / \", p_r3.getLastPage(), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", p_r3.pages)(\"ngForTrackBy\", ctx_r3.trackByIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.directionLinks);\n  }\n}\nclass PaginationService {\n  constructor() {\n    this.change = new EventEmitter();\n    this.instances = {};\n    this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\n  }\n  defaultId() {\n    return this.DEFAULT_ID;\n  }\n  /**\r\n   * Register a PaginationInstance with this service. Returns a\r\n   * boolean value signifying whether the instance is new or\r\n   * updated (true = new or updated, false = unchanged).\r\n   */\n  register(instance) {\n    if (instance.id == null) {\n      instance.id = this.DEFAULT_ID;\n    }\n    if (!this.instances[instance.id]) {\n      this.instances[instance.id] = instance;\n      return true;\n    } else {\n      return this.updateInstance(instance);\n    }\n  }\n  /**\r\n   * Check each property of the instance and update any that have changed. Return\r\n   * true if any changes were made, else return false.\r\n   */\n  updateInstance(instance) {\n    let changed = false;\n    for (let prop in this.instances[instance.id]) {\n      if (instance[prop] !== this.instances[instance.id][prop]) {\n        this.instances[instance.id][prop] = instance[prop];\n        changed = true;\n      }\n    }\n    return changed;\n  }\n  /**\r\n   * Returns the current page number.\r\n   */\n  getCurrentPage(id) {\n    if (this.instances[id]) {\n      return this.instances[id].currentPage;\n    }\n    return 1;\n  }\n  /**\r\n   * Sets the current page number.\r\n   */\n  setCurrentPage(id, page) {\n    if (this.instances[id]) {\n      let instance = this.instances[id];\n      let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\n      if (page <= maxPage && 1 <= page) {\n        this.instances[id].currentPage = page;\n        this.change.emit(id);\n      }\n    }\n  }\n  /**\r\n   * Sets the value of instance.totalItems\r\n   */\n  setTotalItems(id, totalItems) {\n    if (this.instances[id] && 0 <= totalItems) {\n      this.instances[id].totalItems = totalItems;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Sets the value of instance.itemsPerPage.\r\n   */\n  setItemsPerPage(id, itemsPerPage) {\n    if (this.instances[id]) {\n      this.instances[id].itemsPerPage = itemsPerPage;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Returns a clone of the pagination instance object matching the id. If no\r\n   * id specified, returns the instance corresponding to the default id.\r\n   */\n  getInstance(id = this.DEFAULT_ID) {\n    if (this.instances[id]) {\n      return this.clone(this.instances[id]);\n    }\n    return {};\n  }\n  /**\r\n   * Perform a shallow clone of an object.\r\n   */\n  clone(obj) {\n    var target = {};\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        target[i] = obj[i];\n      }\n    }\n    return target;\n  }\n}\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\nclass PaginatePipe {\n  constructor(service) {\n    this.service = service;\n    // store the values from the last time the pipe was invoked\n    this.state = {};\n  }\n  transform(collection, args) {\n    // When an observable is passed through the AsyncPipe, it will output\n    // `null` until the subscription resolves. In this case, we want to\n    // use the cached data from the `state` object to prevent the NgFor\n    // from flashing empty until the real values arrive.\n    if (!(collection instanceof Array)) {\n      let _id = args.id || this.service.defaultId();\n      if (this.state[_id]) {\n        return this.state[_id].slice;\n      } else {\n        return collection;\n      }\n    }\n    let serverSideMode = args.totalItems && args.totalItems !== collection.length;\n    let instance = this.createInstance(collection, args);\n    let id = instance.id;\n    let start, end;\n    let perPage = instance.itemsPerPage;\n    let emitChange = this.service.register(instance);\n    if (!serverSideMode && collection instanceof Array) {\n      perPage = +perPage || LARGE_NUMBER;\n      start = (instance.currentPage - 1) * perPage;\n      end = start + perPage;\n      let isIdentical = this.stateIsIdentical(id, collection, start, end);\n      if (isIdentical) {\n        return this.state[id].slice;\n      } else {\n        let slice = collection.slice(start, end);\n        this.saveState(id, collection, slice, start, end);\n        this.service.change.emit(id);\n        return slice;\n      }\n    } else {\n      if (emitChange) {\n        this.service.change.emit(id);\n      }\n      // save the state for server-side collection to avoid null\n      // flash as new data loads.\n      this.saveState(id, collection, collection, start, end);\n      return collection;\n    }\n  }\n  /**\r\n   * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n   */\n  createInstance(collection, config) {\n    this.checkConfig(config);\n    return {\n      id: config.id != null ? config.id : this.service.defaultId(),\n      itemsPerPage: +config.itemsPerPage || 0,\n      currentPage: +config.currentPage || 1,\n      totalItems: +config.totalItems || collection.length\n    };\n  }\n  /**\r\n   * Ensure the argument passed to the filter contains the required properties.\r\n   */\n  checkConfig(config) {\n    const required = ['itemsPerPage', 'currentPage'];\n    const missing = required.filter(prop => !(prop in config));\n    if (0 < missing.length) {\n      throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\n    }\n  }\n  /**\r\n   * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n   * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n   * need to check that the collection, start and end points are all identical, and if so, return the\r\n   * last sliced array.\r\n   */\n  saveState(id, collection, slice, start, end) {\n    this.state[id] = {\n      collection,\n      size: collection.length,\n      slice,\n      start,\n      end\n    };\n  }\n  /**\r\n   * For a given id, returns true if the collection, size, start and end values are identical.\r\n   */\n  stateIsIdentical(id, collection, start, end) {\n    let state = this.state[id];\n    if (!state) {\n      return false;\n    }\n    let isMetaDataIdentical = state.size === collection.length && state.start === start && state.end === end;\n    if (!isMetaDataIdentical) {\n      return false;\n    }\n    return state.slice.every((element, index) => element === collection[start + index]);\n  }\n}\nPaginatePipe.ɵfac = function PaginatePipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginatePipe)(i0.ɵɵdirectiveInject(PaginationService, 16));\n};\nPaginatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"paginate\",\n  type: PaginatePipe,\n  pure: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'paginate',\n      pure: false\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }];\n  }, null);\n})();\n\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\nconst DEFAULT_TEMPLATE = `\n    <pagination-template  #p=\"paginationApi\"\n                         [id]=\"id\"\n                         [maxSize]=\"maxSize\"\n                         (pageChange)=\"pageChange.emit($event)\"\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\n    <ul class=\"ngx-pagination\" \n        [class.responsive]=\"responsive\"\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\n\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li> \n\n        <li class=\"small-screen\">\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\n        </li>\n\n        <li [class.current]=\"p.getCurrent() === page.value\" \n            [class.ellipsis]=\"page.label === '...'\"\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\n            </a>\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\n              <span aria-live=\"polite\">\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \n              </span>\n            </ng-container>\n        </li>\n\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li>\n\n    </ul>\n    </nav>\n    </pagination-template>\n    `;\nconst DEFAULT_STYLES = `\n.ngx-pagination {\n  margin-left: 0;\n  margin-bottom: 1rem; }\n  .ngx-pagination::before, .ngx-pagination::after {\n    content: ' ';\n    display: table; }\n  .ngx-pagination::after {\n    clear: both; }\n  .ngx-pagination li {\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    margin-right: 0.0625rem;\n    border-radius: 0; }\n  .ngx-pagination li {\n    display: inline-block; }\n  .ngx-pagination a,\n  .ngx-pagination button {\n    color: #0a0a0a; \n    display: block;\n    padding: 0.1875rem 0.625rem;\n    border-radius: 0; }\n    .ngx-pagination a:hover,\n    .ngx-pagination button:hover {\n      background: #e6e6e6; }\n  .ngx-pagination .current {\n    padding: 0.1875rem 0.625rem;\n    background: #2199e8;\n    color: #fefefe;\n    cursor: default; }\n  .ngx-pagination .disabled {\n    padding: 0.1875rem 0.625rem;\n    color: #cacaca;\n    cursor: default; } \n    .ngx-pagination .disabled:hover {\n      background: transparent; }\n  .ngx-pagination a, .ngx-pagination button {\n    cursor: pointer; }\n\n.ngx-pagination .pagination-previous a::before,\n.ngx-pagination .pagination-previous.disabled::before { \n  content: '«';\n  display: inline-block;\n  margin-right: 0.5rem; }\n\n.ngx-pagination .pagination-next a::after,\n.ngx-pagination .pagination-next.disabled::after {\n  content: '»';\n  display: inline-block;\n  margin-left: 0.5rem; }\n\n.ngx-pagination .show-for-sr {\n  position: absolute !important;\n  width: 1px;\n  height: 1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0); }\n.ngx-pagination .small-screen {\n  display: none; }\n@media screen and (max-width: 601px) {\n  .ngx-pagination.responsive .small-screen {\n    display: inline-block; } \n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\n    display: none; }\n}\n  `;\n\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\nclass PaginationControlsDirective {\n  constructor(service, changeDetectorRef) {\n    this.service = service;\n    this.changeDetectorRef = changeDetectorRef;\n    this.maxSize = 7;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this.pages = [];\n    this.changeSub = this.service.change.subscribe(id => {\n      if (this.id === id) {\n        this.updatePageLinks();\n        this.changeDetectorRef.markForCheck();\n        this.changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngOnInit() {\n    if (this.id === undefined) {\n      this.id = this.service.defaultId();\n    }\n    this.updatePageLinks();\n  }\n  ngOnChanges(changes) {\n    this.updatePageLinks();\n  }\n  ngOnDestroy() {\n    this.changeSub.unsubscribe();\n  }\n  /**\r\n   * Go to the previous page\r\n   */\n  previous() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() - 1);\n  }\n  /**\r\n   * Go to the next page\r\n   */\n  next() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() + 1);\n  }\n  /**\r\n   * Returns true if current page is first page\r\n   */\n  isFirstPage() {\n    return this.getCurrent() === 1;\n  }\n  /**\r\n   * Returns true if current page is last page\r\n   */\n  isLastPage() {\n    return this.getLastPage() === this.getCurrent();\n  }\n  /**\r\n   * Set the current page number.\r\n   */\n  setCurrent(page) {\n    this.pageChange.emit(page);\n  }\n  /**\r\n   * Get the current page number.\r\n   */\n  getCurrent() {\n    return this.service.getCurrentPage(this.id);\n  }\n  /**\r\n   * Returns the last page number\r\n   */\n  getLastPage() {\n    let inst = this.service.getInstance(this.id);\n    if (inst.totalItems < 1) {\n      // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\n      // but it makes sense to consider a single, empty page as the last page.\n      return 1;\n    }\n    return Math.ceil(inst.totalItems / inst.itemsPerPage);\n  }\n  getTotalItems() {\n    return this.service.getInstance(this.id).totalItems;\n  }\n  checkValidId() {\n    if (this.service.getInstance(this.id).id == null) {\n      console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\n    }\n  }\n  /**\r\n   * Updates the page links and checks that the current page is valid. Should run whenever the\r\n   * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n   * input values changes.\r\n   */\n  updatePageLinks() {\n    let inst = this.service.getInstance(this.id);\n    const correctedCurrentPage = this.outOfBoundCorrection(inst);\n    if (correctedCurrentPage !== inst.currentPage) {\n      setTimeout(() => {\n        this.pageBoundsCorrection.emit(correctedCurrentPage);\n        this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n      });\n    } else {\n      this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n    }\n  }\n  /**\r\n   * Checks that the instance.currentPage property is within bounds for the current page range.\r\n   * If not, return a correct value for currentPage, or the current value if OK.\r\n   */\n  outOfBoundCorrection(instance) {\n    const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\n    if (totalPages < instance.currentPage && 0 < totalPages) {\n      return totalPages;\n    } else if (instance.currentPage < 1) {\n      return 1;\n    }\n    return instance.currentPage;\n  }\n  /**\r\n   * Returns an array of Page objects to use in the pagination controls.\r\n   */\n  createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\n    // paginationRange could be a string if passed from attribute, so cast to number.\n    paginationRange = +paginationRange;\n    let pages = [];\n    // Return 1 as default page number\n    // Make sense to show 1 instead of empty when there are no items\n    const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\n    const halfWay = Math.ceil(paginationRange / 2);\n    const isStart = currentPage <= halfWay;\n    const isEnd = totalPages - halfWay < currentPage;\n    const isMiddle = !isStart && !isEnd;\n    let ellipsesNeeded = paginationRange < totalPages;\n    let i = 1;\n    while (i <= totalPages && i <= paginationRange) {\n      let label;\n      let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\n      let openingEllipsesNeeded = i === 2 && (isMiddle || isEnd);\n      let closingEllipsesNeeded = i === paginationRange - 1 && (isMiddle || isStart);\n      if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\n        label = '...';\n      } else {\n        label = pageNumber;\n      }\n      pages.push({\n        label: label,\n        value: pageNumber\n      });\n      i++;\n    }\n    return pages;\n  }\n  /**\r\n   * Given the position in the sequence of pagination links [i],\r\n   * figure out what page number corresponds to that position.\r\n   */\n  calculatePageNumber(i, currentPage, paginationRange, totalPages) {\n    let halfWay = Math.ceil(paginationRange / 2);\n    if (i === paginationRange) {\n      return totalPages;\n    } else if (i === 1) {\n      return i;\n    } else if (paginationRange < totalPages) {\n      if (totalPages - halfWay < currentPage) {\n        return totalPages - paginationRange + i;\n      } else if (halfWay < currentPage) {\n        return currentPage - halfWay + i;\n      } else {\n        return i;\n      }\n    } else {\n      return i;\n    }\n  }\n}\nPaginationControlsDirective.ɵfac = function PaginationControlsDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginationControlsDirective)(i0.ɵɵdirectiveInject(PaginationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nPaginationControlsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PaginationControlsDirective,\n  selectors: [[\"pagination-template\"], [\"\", \"pagination-template\", \"\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  exportAs: [\"paginationApi\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'pagination-template,[pagination-template]',\n      exportAs: 'paginationApi'\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\nfunction coerceToBoolean(input) {\n  return !!input && input !== 'false';\n}\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\nclass PaginationControlsComponent {\n  constructor() {\n    this.maxSize = 7;\n    this.previousLabel = 'Previous';\n    this.nextLabel = 'Next';\n    this.screenReaderPaginationLabel = 'Pagination';\n    this.screenReaderPageLabel = 'page';\n    this.screenReaderCurrentLabel = `You're on page`;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this._directionLinks = true;\n    this._autoHide = false;\n    this._responsive = false;\n  }\n  get directionLinks() {\n    return this._directionLinks;\n  }\n  set directionLinks(value) {\n    this._directionLinks = coerceToBoolean(value);\n  }\n  get autoHide() {\n    return this._autoHide;\n  }\n  set autoHide(value) {\n    this._autoHide = coerceToBoolean(value);\n  }\n  get responsive() {\n    return this._responsive;\n  }\n  set responsive(value) {\n    this._responsive = coerceToBoolean(value);\n  }\n  trackByIndex(index) {\n    return index;\n  }\n}\nPaginationControlsComponent.ɵfac = function PaginationControlsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PaginationControlsComponent)();\n};\nPaginationControlsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PaginationControlsComponent,\n  selectors: [[\"pagination-controls\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\",\n    directionLinks: \"directionLinks\",\n    autoHide: \"autoHide\",\n    responsive: \"responsive\",\n    previousLabel: \"previousLabel\",\n    nextLabel: \"nextLabel\",\n    screenReaderPaginationLabel: \"screenReaderPaginationLabel\",\n    screenReaderPageLabel: \"screenReaderPageLabel\",\n    screenReaderCurrentLabel: \"screenReaderCurrentLabel\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  decls: 4,\n  vars: 4,\n  consts: [[\"p\", \"paginationApi\"], [3, \"pageChange\", \"pageBoundsCorrection\", \"id\", \"maxSize\"], [\"role\", \"navigation\"], [\"class\", \"ngx-pagination\", 3, \"responsive\", 4, \"ngIf\"], [1, \"ngx-pagination\"], [\"class\", \"pagination-previous\", 3, \"disabled\", 4, \"ngIf\"], [1, \"small-screen\"], [3, \"current\", \"ellipsis\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-next\", 3, \"disabled\", 4, \"ngIf\"], [1, \"pagination-previous\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\", 4, \"ngIf\"], [\"aria-disabled\", \"true\", 4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\"], [1, \"show-for-sr\"], [\"aria-disabled\", \"true\"], [4, \"ngIf\"], [\"aria-live\", \"polite\"], [1, \"pagination-next\"]],\n  template: function PaginationControlsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"pagination-template\", 1, 0);\n      i0.ɵɵlistener(\"pageChange\", function PaginationControlsComponent_Template_pagination_template_pageChange_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.pageChange.emit($event));\n      })(\"pageBoundsCorrection\", function PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.pageBoundsCorrection.emit($event));\n      });\n      i0.ɵɵelementStart(2, \"nav\", 2);\n      i0.ɵɵtemplate(3, PaginationControlsComponent_ul_3_Template, 6, 8, \"ul\", 3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const p_r3 = i0.ɵɵreference(1);\n      i0.ɵɵproperty(\"id\", ctx.id)(\"maxSize\", ctx.maxSize);\n      i0.ɵɵadvance(2);\n      i0.ɵɵattribute(\"aria-label\", ctx.screenReaderPaginationLabel);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !(ctx.autoHide && p_r3.pages.length <= 1));\n    }\n  },\n  dependencies: [PaginationControlsDirective, i2.NgIf, i2.NgForOf, i2.DecimalPipe],\n  styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'pagination-controls',\n      template: DEFAULT_TEMPLATE,\n      styles: [DEFAULT_STYLES],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    directionLinks: [{\n      type: Input\n    }],\n    autoHide: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    previousLabel: [{\n      type: Input\n    }],\n    nextLabel: [{\n      type: Input\n    }],\n    screenReaderPaginationLabel: [{\n      type: Input\n    }],\n    screenReaderPageLabel: [{\n      type: Input\n    }],\n    screenReaderCurrentLabel: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\nclass NgxPaginationModule {}\nNgxPaginationModule.ɵfac = function NgxPaginationModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || NgxPaginationModule)();\n};\nNgxPaginationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxPaginationModule,\n  declarations: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective],\n  imports: [CommonModule],\n  exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\n});\nNgxPaginationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [PaginationService],\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective],\n      providers: [PaginationService],\n      exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,eAAe,SAAS,8EAA8E;AAClH,MAAG,cAAc,GAAG;AACpB,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,SAAS,CAAC;AAAA,IACvC,CAAC,EAAE,SAAS,SAAS,wEAAwE;AAC3F,MAAG,cAAc,GAAG;AACpB,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,SAAS,CAAC;AAAA,IACvC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,eAAe,GAAG;AACpD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,qBAAqB;AAAA,EACnD;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,eAAe,GAAG;AACpD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,qBAAqB;AAAA,EACnD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,uDAAuD,GAAG,GAAG,QAAQ,EAAE;AAC9J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,OAAU,YAAY,CAAC;AAC7B,IAAG,YAAY,YAAY,KAAK,YAAY,CAAC;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,IAAI,KAAK,WAAW,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,KAAK,YAAY,CAAC;AAAA,EAC1C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,eAAe,SAAS,8EAA8E;AAClH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,EAAE;AACnC,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,WAAW,QAAQ,KAAK,CAAC;AAAA,IACtD,CAAC,EAAE,SAAS,SAAS,wEAAwE;AAC3F,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,EAAE;AACnC,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,WAAW,QAAQ,KAAK,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,OAAO,uBAAuB,GAAG;AAC3D,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,QAAQ,UAAU,QAAQ,QAAQ,QAAW,YAAY,GAAG,GAAG,QAAQ,OAAO,EAAE,CAAC;AAAA,EACxG;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,EAAE,EAAE,GAAG,QAAQ,EAAE;AAC9C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,aAAa,EAAE;AAClB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,IAAI,OAAO,0BAA0B,GAAG;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,QAAQ,UAAU,QAAQ,QAAQ,QAAW,YAAY,GAAG,GAAG,QAAQ,OAAO,EAAE,CAAC;AAAA,EACxG;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,+DAA+D,GAAG,GAAG,gBAAgB,EAAE;AAC9K,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,cAAc,CAAC;AAClB,UAAM,OAAU,YAAY,CAAC;AAC7B,IAAG,YAAY,WAAW,KAAK,WAAW,MAAM,QAAQ,KAAK,EAAE,YAAY,QAAQ,UAAU,KAAK;AAClG,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK;AACzD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK;AAAA,EAC3D;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,eAAe,SAAS,8EAA8E;AAClH,MAAG,cAAc,GAAG;AACpB,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,KAAK,CAAC;AAAA,IACnC,CAAC,EAAE,SAAS,SAAS,wEAAwE;AAC3F,MAAG,cAAc,GAAG;AACpB,MAAG,cAAc,CAAC;AAClB,YAAM,OAAU,YAAY,CAAC;AAC7B,aAAU,YAAY,KAAK,KAAK,CAAC;AAAA,IACnC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,WAAW,GAAG;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,qBAAqB;AAAA,EACnD;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,WAAW,GAAG;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,qBAAqB;AAAA,EACnD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,KAAK,EAAE,EAAE,GAAG,uDAAuD,GAAG,GAAG,QAAQ,EAAE;AAC9J,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,OAAU,YAAY,CAAC;AAC7B,IAAG,YAAY,YAAY,KAAK,WAAW,CAAC;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,KAAK,WAAW,CAAC;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,KAAK,WAAW,CAAC;AAAA,EACzC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC;AAC9E,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC;AAChJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,OAAU,YAAY,CAAC;AAC7B,IAAG,YAAY,cAAc,OAAO,UAAU;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,KAAK,WAAW,GAAG,OAAO,KAAK,YAAY,GAAG,GAAG;AAC5E,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,KAAK,KAAK,EAAE,gBAAgB,OAAO,YAAY;AACxE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc;AAAA,EAC7C;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,SAAS,IAAI,aAAa;AAC/B,SAAK,YAAY,CAAC;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,UAAU;AACjB,QAAI,SAAS,MAAM,MAAM;AACvB,eAAS,KAAK,KAAK;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,UAAU,SAAS,EAAE,GAAG;AAChC,WAAK,UAAU,SAAS,EAAE,IAAI;AAC9B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,eAAe,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,UAAU;AACvB,QAAI,UAAU;AACd,aAAS,QAAQ,KAAK,UAAU,SAAS,EAAE,GAAG;AAC5C,UAAI,SAAS,IAAI,MAAM,KAAK,UAAU,SAAS,EAAE,EAAE,IAAI,GAAG;AACxD,aAAK,UAAU,SAAS,EAAE,EAAE,IAAI,IAAI,SAAS,IAAI;AACjD,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,IAAI;AACjB,QAAI,KAAK,UAAU,EAAE,GAAG;AACtB,aAAO,KAAK,UAAU,EAAE,EAAE;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,IAAI,MAAM;AACvB,QAAI,KAAK,UAAU,EAAE,GAAG;AACtB,UAAI,WAAW,KAAK,UAAU,EAAE;AAChC,UAAI,UAAU,KAAK,KAAK,SAAS,aAAa,SAAS,YAAY;AACnE,UAAI,QAAQ,WAAW,KAAK,MAAM;AAChC,aAAK,UAAU,EAAE,EAAE,cAAc;AACjC,aAAK,OAAO,KAAK,EAAE;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,IAAI,YAAY;AAC5B,QAAI,KAAK,UAAU,EAAE,KAAK,KAAK,YAAY;AACzC,WAAK,UAAU,EAAE,EAAE,aAAa;AAChC,WAAK,OAAO,KAAK,EAAE;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,IAAI,cAAc;AAChC,QAAI,KAAK,UAAU,EAAE,GAAG;AACtB,WAAK,UAAU,EAAE,EAAE,eAAe;AAClC,WAAK,OAAO,KAAK,EAAE;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK,KAAK,YAAY;AAChC,QAAI,KAAK,UAAU,EAAE,GAAG;AACtB,aAAO,KAAK,MAAM,KAAK,UAAU,EAAE,CAAC;AAAA,IACtC;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,KAAK;AACT,QAAI,SAAS,CAAC;AACd,aAAS,KAAK,KAAK;AACjB,UAAI,IAAI,eAAe,CAAC,GAAG;AACzB,eAAO,CAAC,IAAI,IAAI,CAAC;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,eAAe,OAAO;AAC5B,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS;AACnB,SAAK,UAAU;AAEf,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,UAAU,YAAY,MAAM;AAK1B,QAAI,EAAE,sBAAsB,QAAQ;AAClC,UAAI,MAAM,KAAK,MAAM,KAAK,QAAQ,UAAU;AAC5C,UAAI,KAAK,MAAM,GAAG,GAAG;AACnB,eAAO,KAAK,MAAM,GAAG,EAAE;AAAA,MACzB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,iBAAiB,KAAK,cAAc,KAAK,eAAe,WAAW;AACvE,QAAI,WAAW,KAAK,eAAe,YAAY,IAAI;AACnD,QAAI,KAAK,SAAS;AAClB,QAAI,OAAO;AACX,QAAI,UAAU,SAAS;AACvB,QAAI,aAAa,KAAK,QAAQ,SAAS,QAAQ;AAC/C,QAAI,CAAC,kBAAkB,sBAAsB,OAAO;AAClD,gBAAU,CAAC,WAAW;AACtB,eAAS,SAAS,cAAc,KAAK;AACrC,YAAM,QAAQ;AACd,UAAI,cAAc,KAAK,iBAAiB,IAAI,YAAY,OAAO,GAAG;AAClE,UAAI,aAAa;AACf,eAAO,KAAK,MAAM,EAAE,EAAE;AAAA,MACxB,OAAO;AACL,YAAI,QAAQ,WAAW,MAAM,OAAO,GAAG;AACvC,aAAK,UAAU,IAAI,YAAY,OAAO,OAAO,GAAG;AAChD,aAAK,QAAQ,OAAO,KAAK,EAAE;AAC3B,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAI,YAAY;AACd,aAAK,QAAQ,OAAO,KAAK,EAAE;AAAA,MAC7B;AAGA,WAAK,UAAU,IAAI,YAAY,YAAY,OAAO,GAAG;AACrD,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,YAAY,QAAQ;AACjC,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,MACL,IAAI,OAAO,MAAM,OAAO,OAAO,KAAK,KAAK,QAAQ,UAAU;AAAA,MAC3D,cAAc,CAAC,OAAO,gBAAgB;AAAA,MACtC,aAAa,CAAC,OAAO,eAAe;AAAA,MACpC,YAAY,CAAC,OAAO,cAAc,WAAW;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,QAAQ;AAClB,UAAM,WAAW,CAAC,gBAAgB,aAAa;AAC/C,UAAM,UAAU,SAAS,OAAO,UAAQ,EAAE,QAAQ,OAAO;AACzD,QAAI,IAAI,QAAQ,QAAQ;AACtB,YAAM,IAAI,MAAM,wEAAwE,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,IAC9G;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,IAAI,YAAY,OAAO,OAAO,KAAK;AAC3C,SAAK,MAAM,EAAE,IAAI;AAAA,MACf;AAAA,MACA,MAAM,WAAW;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,IAAI,YAAY,OAAO,KAAK;AAC3C,QAAI,QAAQ,KAAK,MAAM,EAAE;AACzB,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,sBAAsB,MAAM,SAAS,WAAW,UAAU,MAAM,UAAU,SAAS,MAAM,QAAQ;AACrG,QAAI,CAAC,qBAAqB;AACxB,aAAO;AAAA,IACT;AACA,WAAO,MAAM,MAAM,MAAM,CAAC,SAAS,UAAU,YAAY,WAAW,QAAQ,KAAK,CAAC;AAAA,EACpF;AACF;AACA,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAiB,kBAAkB,mBAAmB,EAAE,CAAC;AAC5F;AACA,aAAa,QAA0B,aAAa;AAAA,EAClD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAMH,IAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoDzB,IAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyEvB,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAY,SAAS,mBAAmB;AACtC,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,QAAQ,CAAC;AACd,SAAK,YAAY,KAAK,QAAQ,OAAO,UAAU,QAAM;AACnD,UAAI,KAAK,OAAO,IAAI;AAClB,aAAK,gBAAgB;AACrB,aAAK,kBAAkB,aAAa;AACpC,aAAK,kBAAkB,cAAc;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,QAAI,KAAK,OAAO,QAAW;AACzB,WAAK,KAAK,KAAK,QAAQ,UAAU;AAAA,IACnC;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACT,SAAK,aAAa;AAClB,SAAK,WAAW,KAAK,WAAW,IAAI,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,SAAK,aAAa;AAClB,SAAK,WAAW,KAAK,WAAW,IAAI,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,WAAO,KAAK,YAAY,MAAM,KAAK,WAAW;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM;AACf,SAAK,WAAW,KAAK,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,WAAO,KAAK,QAAQ,eAAe,KAAK,EAAE;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,OAAO,KAAK,QAAQ,YAAY,KAAK,EAAE;AAC3C,QAAI,KAAK,aAAa,GAAG;AAGvB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,KAAK,aAAa,KAAK,YAAY;AAAA,EACtD;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,QAAQ,YAAY,KAAK,EAAE,EAAE;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,QAAI,KAAK,QAAQ,YAAY,KAAK,EAAE,EAAE,MAAM,MAAM;AAChD,cAAQ,KAAK,kDAAkD,KAAK,EAAE,oDAAoD;AAAA,IAC5H;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,OAAO,KAAK,QAAQ,YAAY,KAAK,EAAE;AAC3C,UAAM,uBAAuB,KAAK,qBAAqB,IAAI;AAC3D,QAAI,yBAAyB,KAAK,aAAa;AAC7C,iBAAW,MAAM;AACf,aAAK,qBAAqB,KAAK,oBAAoB;AACnD,aAAK,QAAQ,KAAK,gBAAgB,KAAK,aAAa,KAAK,cAAc,KAAK,YAAY,KAAK,OAAO;AAAA,MACtG,CAAC;AAAA,IACH,OAAO;AACL,WAAK,QAAQ,KAAK,gBAAgB,KAAK,aAAa,KAAK,cAAc,KAAK,YAAY,KAAK,OAAO;AAAA,IACtG;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,UAAU;AAC7B,UAAM,aAAa,KAAK,KAAK,SAAS,aAAa,SAAS,YAAY;AACxE,QAAI,aAAa,SAAS,eAAe,IAAI,YAAY;AACvD,aAAO;AAAA,IACT,WAAW,SAAS,cAAc,GAAG;AACnC,aAAO;AAAA,IACT;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,aAAa,cAAc,YAAY,iBAAiB;AAEtE,sBAAkB,CAAC;AACnB,QAAI,QAAQ,CAAC;AAGb,UAAM,aAAa,KAAK,IAAI,KAAK,KAAK,aAAa,YAAY,GAAG,CAAC;AACnE,UAAM,UAAU,KAAK,KAAK,kBAAkB,CAAC;AAC7C,UAAM,UAAU,eAAe;AAC/B,UAAM,QAAQ,aAAa,UAAU;AACrC,UAAM,WAAW,CAAC,WAAW,CAAC;AAC9B,QAAI,iBAAiB,kBAAkB;AACvC,QAAI,IAAI;AACR,WAAO,KAAK,cAAc,KAAK,iBAAiB;AAC9C,UAAI;AACJ,UAAI,aAAa,KAAK,oBAAoB,GAAG,aAAa,iBAAiB,UAAU;AACrF,UAAI,wBAAwB,MAAM,MAAM,YAAY;AACpD,UAAI,wBAAwB,MAAM,kBAAkB,MAAM,YAAY;AACtE,UAAI,mBAAmB,yBAAyB,wBAAwB;AACtE,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ;AAAA,MACV;AACA,YAAM,KAAK;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AACD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,GAAG,aAAa,iBAAiB,YAAY;AAC/D,QAAI,UAAU,KAAK,KAAK,kBAAkB,CAAC;AAC3C,QAAI,MAAM,iBAAiB;AACzB,aAAO;AAAA,IACT,WAAW,MAAM,GAAG;AAClB,aAAO;AAAA,IACT,WAAW,kBAAkB,YAAY;AACvC,UAAI,aAAa,UAAU,aAAa;AACtC,eAAO,aAAa,kBAAkB;AAAA,MACxC,WAAW,UAAU,aAAa;AAChC,eAAO,cAAc,UAAU;AAAA,MACjC,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAAgC,kBAAkB,iBAAiB,GAAM,kBAAqB,iBAAiB,CAAC;AACnJ;AACA,4BAA4B,OAAyB,kBAAkB;AAAA,EACrE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,EACpE,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AAAA,IACZ,sBAAsB;AAAA,EACxB;AAAA,EACA,UAAU,CAAC,eAAe;AAAA,EAC1B,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,gBAAgB,OAAO;AAC9B,SAAO,CAAC,CAAC,SAAS,UAAU;AAC9B;AAIA,IAAM,8BAAN,MAAkC;AAAA,EAChC,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,8BAA8B;AACnC,SAAK,wBAAwB;AAC7B,SAAK,2BAA2B;AAChC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,uBAAuB,IAAI,aAAa;AAC7C,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,kBAAkB,gBAAgB,KAAK;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,gBAAgB,KAAK;AAAA,EACxC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,gBAAgB,KAAK;AAAA,EAC1C;AAAA,EACA,aAAa,OAAO;AAClB,WAAO;AAAA,EACT;AACF;AACA,4BAA4B,OAAO,SAAS,oCAAoC,mBAAmB;AACjG,SAAO,KAAK,qBAAqB,6BAA6B;AAChE;AACA,4BAA4B,OAAyB,kBAAkB;AAAA,EACrE,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,EACnC,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,EAC5B;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AAAA,IACZ,sBAAsB;AAAA,EACxB;AAAA,EACA,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,KAAK,eAAe,GAAG,CAAC,GAAG,cAAc,wBAAwB,MAAM,SAAS,GAAG,CAAC,QAAQ,YAAY,GAAG,CAAC,SAAS,kBAAkB,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,SAAS,uBAAuB,GAAG,YAAY,GAAG,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,WAAW,YAAY,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,SAAS,mBAAmB,GAAG,YAAY,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,YAAY,KAAK,GAAG,eAAe,SAAS,GAAG,MAAM,GAAG,CAAC,iBAAiB,QAAQ,GAAG,MAAM,GAAG,CAAC,YAAY,KAAK,GAAG,eAAe,OAAO,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,iBAAiB,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,aAAa,QAAQ,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,EACrqB,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,QAAI,KAAK,GAAG;AACV,YAAM,MAAS,iBAAiB;AAChC,MAAG,eAAe,GAAG,uBAAuB,GAAG,CAAC;AAChD,MAAG,WAAW,cAAc,SAAS,+EAA+E,QAAQ;AAC1H,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,WAAW,KAAK,MAAM,CAAC;AAAA,MACnD,CAAC,EAAE,wBAAwB,SAAS,yFAAyF,QAAQ;AACnI,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,qBAAqB,KAAK,MAAM,CAAC;AAAA,MAC7D,CAAC;AACD,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,MAAM,CAAC;AACzE,MAAG,aAAa,EAAE;AAAA,IACpB;AACA,QAAI,KAAK,GAAG;AACV,YAAM,OAAU,YAAY,CAAC;AAC7B,MAAG,WAAW,MAAM,IAAI,EAAE,EAAE,WAAW,IAAI,OAAO;AAClD,MAAG,UAAU,CAAC;AACd,MAAG,YAAY,cAAc,IAAI,2BAA2B;AAC5D,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,EAAE,IAAI,YAAY,KAAK,MAAM,UAAU,EAAE;AAAA,IACjE;AAAA,EACF;AAAA,EACA,cAAc,CAAC,6BAAgC,MAAS,SAAY,WAAW;AAAA,EAC/E,QAAQ,CAAC,g7CAAs7C;AAAA,EAC/7C,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ,CAAC,cAAc;AAAA,MACvB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAA0B;AAAC;AAC3B,oBAAoB,OAAO,SAAS,4BAA4B,mBAAmB;AACjF,SAAO,KAAK,qBAAqB,qBAAqB;AACxD;AACA,oBAAoB,OAAyB,iBAAiB;AAAA,EAC5D,MAAM;AAAA,EACN,cAAc,CAAC,cAAc,6BAA6B,2BAA2B;AAAA,EACrF,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,cAAc,6BAA6B,2BAA2B;AAClF,CAAC;AACD,oBAAoB,OAAyB,iBAAiB;AAAA,EAC5D,WAAW,CAAC,iBAAiB;AAAA,EAC7B,SAAS,CAAC,CAAC,YAAY,CAAC;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,cAAc,CAAC,cAAc,6BAA6B,2BAA2B;AAAA,MACrF,WAAW,CAAC,iBAAiB;AAAA,MAC7B,SAAS,CAAC,cAAc,6BAA6B,2BAA2B;AAAA,IAClF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}