/* =======================
   STYLES GLOBAUX
======================= */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Montserrat', sans-serif;
}
table {
  border-collapse: collapse;
  width: 100%;
}

th, td {
  border: 1px solid black;
  padding: 8px;
  text-align: left;
}
.status-stamp {
  position: absolute;
  top: 10px; /* Adjust vertical position */
  left: 40px;

   /* Rotate 45 degrees counterclockwise */
  padding: 10px 20px; /* Increase padding for a stamp-like size */
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
   /* Optional border for stamp effect */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Shadow for depth */
  z-index: 1; /* Ensure it stays above other content */
  text-align: center;
  width: 150px; /* Fixed width to control stamp size */
  background-color: rgba(0, 0, 0, 0.2); /* Semi-transparent background */
  transition: transform 0.3s ease-in-out;
}

/* Approved Stamp (validation = 1) */
.approved-stamp {
  background-color: #4caf50; /* Green for Approved */
}

/* Rejected Stamp (validation = 0) */
.rejected-stamp {
  background-color: #f44336; /* Red for Rejected */
}

/* Hover effect for interactivity */


/* Ensure header has relative positioning for absolute child */
.rapport-header {
  position: relative;
  text-align: center;
  border-bottom: 4px solid #2496d3;
  padding: 30px;
}

/* Adjust header-top to accommodate the stamp */
.header-top {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding-top: 40px; /* Add padding to prevent overlap with the stamp */
}

/* Optional: Ensure the stamp doesn't overlap the content below */
.rapport-container {
  position: relative;
  margin: 40px auto;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.25);
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;
}
body {
  background-color: #eef7fc; /* Bleu ciel léger */
  color: #333;
  line-height: 1.6;
  font-size: 16px;
}
/* =======================
   STYLES POUR LES LOGOS
======================= */
.lab-logo {
  position: absolute;
  left: 20px; /* Position à gauche */
  top: 15px; /* Alignement avec le logo de droite */
  width: 120px; /* Taille du logo */
  height: auto;
  background: white;
  padding: 5px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Ombre douce */
  transition: transform 0.3s ease-in-out;
}

.lab-logo:hover {
  transform: scale(1.05); /* Effet léger de zoom au survol */
}

/* =======================
   CONTENEUR PRINCIPAL
======================= */
.rapport-container {
  position: relative; /* Added for absolute positioning of print button */
  margin: 40px auto;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(36, 150, 211, 0.25); /* Ombre améliorée */
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* =======================
   EN-TÊTE DU RAPPORT
======================= */
.rapport-header {
  text-align: center;
  border-bottom: 4px solid #2496d3;
  padding:30px ;
  position: relative;
}

.rapport-header .header-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.rapport-header h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 26px;
  font-weight: bold;
  color: #2496d3;
  margin-bottom: 10px;
  text-transform: uppercase;
  animation: slideDown 1s ease-in-out;
}

.rapport-header p {
  color: #666;
  font-size: 15px;
  margin-top: 0;
}

/* =======================
   LOGO ACCRÉDITATION
======================= */
.accreditation-logo {
  position: absolute;
  top: 15px;  /* Adjust vertical position */
  right: 20px;  /* Adjust horizontal position */
  width: 100px; /* Set proper size */
  height: auto;
  background: white; /* Ensure contrast */
  padding: 10px;
  border-radius: 8px; /* Rounded corners */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Soft shadow */
  transition: transform 0.3s ease-in-out;
}

.accreditation-logo:hover {
  transform: scale(1.05); /* Small zoom effect on hover */
}

/* =======================
   INFORMATIONS (CLIENT / LABO)
======================= */
.rapport-additional-info {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.info-left,
.info-right {
  width: 100%;
  box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15); /* Ombre améliorée */
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
  transition: transform 0.3s ease-in-out;
}

.info-left:hover,
.info-right:hover {
  transform: translateY(-5px); /* Effet léger de soulèvement */
}

.rapport-additional-info h3 {
  color: #2496d3;
  font-size: 20px;
  margin-bottom: 12px;
  font-weight: bold;
}

.rapport-additional-info table {
  width: 100%;
  border-collapse: collapse;
}

.rapport-additional-info td {
  padding: 10px 0;
  border-bottom: 1px solid #f2f2f2;
}

.rapport-additional-info td.label {
  font-weight: 600;
  width: 35%;
  color: #555;
}

.rapport-additional-info td.input-field,
.rapport-additional-info td.lab-info {
  color: #333;
  font-weight: normal;
}

/* =======================
   CONTENU (DESCRIPTIONS / ANALYSES / RÉSULTATS)
======================= */
.rapport-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Sections générales */
.rapport-description,
.rapport-analyses,
.rapport-resultats {
  box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15);
  padding: 25px;
  border-radius: 10px;
  background-color: #fff;
  transition: transform 0.3s ease-in-out;
}

.rapport-description:hover,
.rapport-analyses:hover,
.rapport-resultats:hover {
  transform: translateY(-5px); /* Effet léger */
}

.rapport-description h2,
.rapport-analyses h2,
.rapport-resultats h2 {
  color: #2496d3;
  font-size: 22px;
  font-weight: 700;
  border-bottom: 3px solid #2496d3;
  padding-bottom: 7px;
  margin-bottom: 18px;
}

/* =======================
   PRINT BUTTON
======================= */
.print-button-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.print-btn {
  padding: 10px 20px;
  font-size: 16px;
  background-color: #2496d3;
  color: #fff;
  border-radius: 6px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease-in-out;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.print-btn:hover {
  background-color: #1a6a8e;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* =======================
   BOUTONS D'ACTION
======================= */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 15px;
}

.action-buttons button {
  padding: 12px 24px;
  font-size: 16px;
  color: #fff;
  border-radius: 6px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease-in-out;
  font-weight: bold;
}

.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.validate-btn {
  background-color: #4CAF50; /* Green color */
  color: white;
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.validate-btn:hover {
  background-color: #3e8e41; /* Darker green on hover */
  transform: scale(1.05);
}

.reject-btn {
  background-color: #e74c3c; /* Red color */
  color: white;
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.reject-btn:hover {
  background-color: #c0392b; /* Darker red on hover */
  transform: scale(1.05);
}

.cancel-btn {
  background-color: #95a5a6;
  color: white;
}

.cancel-btn:hover {
  background-color: #7f8c8d;
}

/* =======================
   MODALS
======================= */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
}

.modal-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  pointer-events: auto;
  animation: fadeIn 0.3s ease-in-out;
}

.modal-content h3 {
  margin-top: 0;
  color: #2496d3;
  font-size: 24px;
  margin-bottom: 20px;
  text-align: center;
}

.modal-content p {
  margin-bottom: 25px;
  font-size: 16px;
  text-align: center;
}

.modal-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.modal-buttons button {
  padding: 10px 25px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  border: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.loading-content p {
  margin: 0;
  font-size: 18px;
  color: #2496d3;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.btn-download {
  background-color: #2496d3;
  font-size: 17px;
  font-weight: bold;
}

.btn-confirm {
  background-color: #4caf50;
}

.btn-confirm:hover {
  background-color: #3e9d40;
}

.btn-cancel {
  background-color: #f44336;
}

.btn-cancel:hover {
  background-color: #e53935;
}

/* =======================
   ANIMATIONS (ADDITIONAL)
======================= */

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =======================
   RESPONSIVE DESIGN
======================= */
@media (max-width: 768px) {
  .rapport-container {
    width: 90%;
    padding: 15px;
  }

  .accreditation-logo {
    width: 100px;
    top: 10px;
    right: 10px;
  }

  .rapport-additional-info {
    flex-direction: column;
  }

  .modal {
    width: 90%;
    max-width: 400px;
  }
}
/* =======================
   TABLE STYLES - Additional Styling
======================= */
/* Table styling already defined above, these are additional styles */

/* Borders for headers (th) */
th {
  background-color: #2496d3; /* Blue header */
  color: white;
  font-weight: bold;
  padding: 12px;
  border: 2px solid #ffffff; /* White borders */
  text-align: center;
}

/* Borders for table rows (tr) and table cells (td) */
tr {
  border-bottom: 1px solid #e0e0e0; /* Light grey border between rows */
  transition: background 0.2s ease-in-out;
}

td {
  padding: 12px;
  border: 1px solid #ddd; /* Grey borders for each cell */
  text-align: center;
  color: #333;
}

/* Alternate row colors for better readability */
tr:nth-child(even) {
  background-color: #f9f9f9;
}

/* Hover effect for better interaction */
tr:hover {
  background-color: #eef7fc; /* Light blue on hover */
}

/* Adding extra padding for specific sections */
.rapport-description table,
.rapport-analyses table,
.rapport-resultats table {
  margin-bottom: 20px;
  border-radius: 10px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  th, td {
    padding: 10px;
    font-size: 14px;
  }
}
