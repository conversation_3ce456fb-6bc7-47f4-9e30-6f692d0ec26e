import { Component, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { RapportsService } from '../rapports-details/rapports.service';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSearch, faEraser, faEye, faFilter, faCalendarAlt, faPaperPlane, faSpinner, faCheck } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-reports-receptionist',
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
  templateUrl: './rapports-list.component.html',
  styleUrls: ['./rapports-list.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class RapportsListDirectorComponent implements OnInit {
  reports: any[] = [];
  filteredReports: any[] = [];
  isLoading: boolean = true; // Loading state

  // Filtering properties
  searchTerm: string = '';
  selectedDate: string = '';
  selectedStatus: string = '';

  // Font Awesome icons
  faSearch = faSearch;
  faEraser = faEraser;
  faEye = faEye;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;
  faPaperPlane = faPaperPlane;
  faSpinner = faSpinner;
  faCheck = faCheck;

  constructor(
    private rapportService: RapportsService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.loadAllRapports();
  }

  // Fetch all reports
  loadAllRapports() {
    this.isLoading = true; // Set loading to true before fetching data

    this.rapportService.getAllRapports().subscribe({
      next: (data) => {
        this.reports = data.map(report => ({
          ...report,
          status: this.getStatusDisplay(report.validation),
          clientStatus: report.client_status === 'sent' ? 'Envoyé' : 'Non envoyé',
          sentToClient: report.client_status === 'sent',
          isSending: false // For tracking the sending state
        }));
        this.filteredReports = [...this.reports]; // Initialize filtered reports
        console.log('Rapports:', this.reports);
        this.isLoading = false; // Set loading to false after data is loaded
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error fetching rapports:', error);
        this.isLoading = false; // Set loading to false even on error
        this.cdr.detectChanges();
      }
    });
  }

  getStatusDisplay(validation: number): string | null {
    switch (validation) {
      case 0: return 'Rejeté';
      case 1: return 'Validé';
      case 2: return null; // Return null instead of empty string
      default: return null;
    }
  }

  // Send a report to client
  sendRapportToClient(demandeId: string, rapportId: number) {
    console.log('Sending report to client with demande ID:', demandeId);

    // Find the report to update its sending status
    const report = this.reports.find(r => r.id === rapportId);
    if (report) {
      report.isSending = true; // Show loading spinner
      this.cdr.detectChanges();
    }

    this.rapportService.sendRapportToClient(demandeId).subscribe({
      next: (response) => {
        console.log('Rapport envoyé au client:', response);
        if (report) {
          report.isSending = false; // Hide loading spinner
          report.sentToClient = true; // Mark as sent to client
          this.cdr.detectChanges();
        }
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi du rapport au client:', error);
        if (report) {
          report.isSending = false; // Hide loading spinner on error too
          this.cdr.detectChanges();
        }
      }
    });
  }

  // ✅ Reject a report
  rejectRapport(rapportId: number) {
    console.log('Rejecting report with ID:', rapportId);
    this.rapportService.updateRapportStatusReject(rapportId).subscribe({
      next: (response) => {
        console.log('Rapport rejeté:', response);
        const report = this.reports.find(r => r.id === rapportId);
        if (report) {
          report.validation = 0;  // Update validation value
          report.status = this.getStatusDisplay(0);  // Update display status
          this.cdr.detectChanges();
        }
        this.cdr.detectChanges();
      },
      error: (error) => console.error('Erreur lors du rejet du rapport:', error)
    });
  }

  validateRapport(rapportId: number) {
    console.log('Validating report with ID:', rapportId);
    this.rapportService.updateRapportStatusValidate(rapportId).subscribe({
      next: (response) => {
        console.log('Rapport validé:', response);
        const report = this.reports.find(r => r.id === rapportId);
        if (report) {
          report.validation = 1;  // Update validation value
          report.status = this.getStatusDisplay(1);  // Update display status
          this.cdr.detectChanges();
        }
        this.cdr.detectChanges();
      },
      error: (error) => console.error('Erreur lors de la validation du rapport:', error)
    });
  }


  // View Report Details
  viewReportDetails(rapportId: number) {
    this.router.navigate(['director/rapportsDetails', rapportId]);
  }

  // Filtering methods
  onFilterChange(): void {
    const term = this.searchTerm.toLowerCase();
    const filterDate = this.selectedDate ? new Date(this.selectedDate) : null;

    this.filteredReports = this.reports.filter((report) => {
      // Filter by search term (ID or demande number)
      const matchesSearch =
        (report.id && report.id.toString().toLowerCase().includes(term)) ||
        (report.demande_id && report.demande_id.toString().toLowerCase().includes(term));

      // Filter by date
      const reportDate = report.creation_date ? new Date(report.creation_date) : null;
      const matchesDate = filterDate && reportDate ?
        reportDate.toDateString() === filterDate.toDateString() :
        !filterDate;

      // Filter by status
      let matchesStatus = true;
      if (this.selectedStatus) {
        if (this.selectedStatus === 'validated') {
          matchesStatus = report.status === 'Validé';
        } else if (this.selectedStatus === 'rejected') {
          matchesStatus = report.status === 'Rejeté';
        } else if (this.selectedStatus === 'pending') {
          matchesStatus = report.status !== 'Validé' && report.status !== 'Rejeté';
        }
      }

      return matchesSearch && matchesDate && matchesStatus;
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedDate = '';
    this.selectedStatus = '';
    this.filteredReports = [...this.reports];
  }
}
