<div class="client-dashboard">
  <div class="dashboard-title-container">
    <div class="circle-icon"><i class="fas fa-user"></i></div>
    <h2 class="dashboard-title">Tableau de bord du Client</h2>
  </div>
  <div class="welcome-message">
    <p>Bienvenue, <strong>{{ name }} {{ nickname }}</strong></p>
  </div>
  <div class="statistics">
    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-clipboard-list"></i></span>
      <h3 class="statistic-title">Demandes actives</h3>
      <p class="statistic-value">{{ nonValidDemandesCount }}</p>
    </div>
    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-file-invoice-dollar"></i></span>
      <h3 class="statistic-title">Devis en attente de paiement</h3>
      <p class="statistic-value">2</p>
    </div>
    <div class="statistic-card">
      <span class="statistic-icon"><i class="fas fa-check-circle"></i></span>
      <h3 class="statistic-title">Réclamations résolues</h3>
      <p class="statistic-value">3</p>
    </div>
  </div>
  <div class="client-actions">
    <button class="btn-client analyses" (click)="goToDemandes()">
      <i class="fas fa-envelope"></i> Demandes d'analyses
    </button>
    <button class="btn-client suivi" (click)="goToSuiviDemande()">
      <i class="fas fa-chart-line"></i> Suivi de la demande
    </button>
    <button class="btn-client devis" (click)="goToDevis()">
      <i class="fas fa-file-alt"></i> Consulter le devis
    </button>

    <button class="btn-client results" (click)="goToResults()">
      <i class="fas fa-flask"></i> Résultats d'analyses
    </button>
    <button class="btn-client reclamation" (click)="goToReclamation()">
      <i class="fas fa-exclamation-triangle"></i> Réclamation
    </button>
    <button class="btn-client reclamations-list" (click)="goToReclamations()">
      <i class="fas fa-list-alt"></i> Mes Réclamations
    </button>
  </div>
</div>