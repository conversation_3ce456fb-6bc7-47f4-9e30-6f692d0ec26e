import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { Demande } from './demande.model';
import { catchError, map, tap } from 'rxjs/operators';
import { Devi<PERSON> } from '../../../models/devis';

@Injectable({
  providedIn: 'root'
})
export class DemandeService {
  private apiUrl = 'http://localhost:8000/api/demande'; // Ensure correct backend URL
  private devisUrl = 'http://localhost:8000/api/user/devis';
  private userDetailsApiUrl = 'http://127.0.0.1:8000/api/userdetails';
  constructor(private http: HttpClient) {}

  getDemandeDetails(demandeId: string): Observable<Demande> {
    console.log(`Fetching demande details for ID: ${demandeId}`);

    return this.http.get<{ demande: Demande }>(`http://localhost:8000/api/demandes/${demandeId}`).pipe(
      map(response => {
        if (!response || !response.demande) {
          throw new Error('Invalid API response structure');
        }
        console.log('Processed API response:', response.demande);
        return response.demande;
      }),
      catchError(error => {
        console.error('API error:', error);
        return throwError(() => new Error('Failed to fetch demande details.'));
      })
    );
  }
  getUserDetails(userId: number): Observable<any> {
    return this.http.get<{ success: boolean; data: any }>(`${this.userDetailsApiUrl}/${userId}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data; // Extracts the actual user data
        } else {
          throw new Error('Invalid API response format for user details.');
        }
      }),
      catchError(error => {
        console.error('Error fetching user details:', error);
        return throwError(() => new Error('Failed to load user details.'));
      })
    );
  }
  getDevis(demandeId: string): Observable<Devis[]> {
    console.log(`Fetching devis for demande ID: ${demandeId}`);

    return this.http.get<any>(`http://127.0.0.1:8000/api/devis/details/${demandeId}`)
      .pipe(
        map(response => {
          console.log('Raw API response for devis:', response);

          // Handle different response structures
          if (response && Array.isArray(response)) {
            console.log('Response is an array, returning directly');
            return response;
          } else if (response && response.devis && Array.isArray(response.devis)) {
            console.log('Response has devis property, extracting it');
            return response.devis;
          } else if (response && response.data && Array.isArray(response.data)) {
            console.log('Response has data property, extracting it');
            return response.data;
          } else {
            console.log('Unexpected response structure, returning empty array');
            return [];
          }
        }),
        catchError(error => {
          console.error('Error fetching devis:', error);
          return of([]); // Return empty array on error
        })
      );
  }
  updateAnalysePrice(demandeId: string, analyse: string, prixUnitaire: number): Observable<any> {
    return this.http.put(`http://127.0.0.1:8000/api/devis/${demandeId}/analyse/${analyse}`, { prix_unitaire: prixUnitaire })
      .pipe(
        tap(response => console.log('Update response:', response)),
        catchError(error => {
          console.error('Error updating analyse price:', error);
          throw error; // Re-throw to handle in component
        })
      );
  }
  getUserDevis(): Observable<Demande[]> {
    return this.http.get<Demande[]>(this.devisUrl);
  }
  updateStatusToOngoing(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/ongoing`, {});
  }
  updateStatusToValidated(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/validate`, {});
  }
  envoyerDevis(demandeId: string): Observable<any> {
    return this.http.post(`http://localhost:8000/api/devis/send/${demandeId}`, {});
  }

  updateStatusToDerogation(demandeId: string): Observable<any> {
    return this.http.patch(`http://localhost:8000/api/receptionist/demandes/${demandeId}/validated-with-derogation`, {});
  }
  createFacture(demandeID: string): Observable<any> {
    return this.http.post<any>(`http://localhost:8000/api/facture/create/${demandeID}`, {});
  }

  /**
   * Get all demandes for the current user
   * @returns Observable of user demandes
   */
  getUserDemandes(): Observable<any> {
     const headers = new HttpHeaders({
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        });
    return this.http.get<any>(`http://localhost:8000/api/demandesUser`, { headers }).pipe(
      catchError(error => {
        console.error('Error fetching user demandes:', error);
        return throwError(() => new Error('Failed to fetch user demandes. Please try again.'));
      })
    );
  }

  /**
   * Count non-valid demandes for the current user
   * @returns Observable with the count of non-valid demandes
   */
  getCountOfNonValidUserDemandes(): Observable<number> {
    return this.getUserDemandes().pipe(
      map(response => {
        console.log('API Response in service:', response);

        let demandesList: any[] = [];

        // Check if response is an array
        if (Array.isArray(response)) {
          demandesList = response;
        }
        // Check if response has a data property that is an array
        else if (response && response.data && Array.isArray(response.data)) {
          demandesList = response.data;
        }
        // Check if response has a demandes property that is an array
        else if (response && response.demandes && Array.isArray(response.demandes)) {
          demandesList = response.demandes;
        }

        // Filter and count non-valid demandes
        return demandesList.filter((demande: any) => demande.status !== 'valid' && demande.status !== 'rejected').length;
      }),
      catchError(error => {
        console.error('Error counting non-valid user demandes:', error);
        return throwError(() => new Error('Failed to count non-valid user demandes. Please try again.'));
      })
    );
  }
}
