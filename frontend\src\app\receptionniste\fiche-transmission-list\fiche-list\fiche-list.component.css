@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Conteneur principal */
.demandes-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 70px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
  display: block;
  justify-content: center;
}

.filter-bar {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping on smaller screens */
  align-items: center; /* Vertically centers all items */
  justify-content: center; /* Horizontally centers all items */
  gap: 1rem; /* Spacing between filter groups and button */
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  max-width: 60%; /* Limits the width as in your original design */
  margin: 0 auto; /* Centers the filter bar within its parent */
}

.filter-group {
  display: flex;
  flex-direction: column; /* Stacks label and input vertically */
  gap: 0.5rem; /* Maintains spacing between label and input */
}
.filter-group label {
  font-weight: 600;
  font-size: 14px;
  color: #444;
}
.filter-group input,
.filter-group select {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}


#search {
  width: 250px;
}

.filter-group input:focus,
.filter-group select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-clear {
  margin-left: auto;
  padding: 10px 15px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;

}


.btn-clear:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

/* Style pour l'icône dans l'input de recherche */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 10px;
  color: #6c757d;
}

.filter-input {
  padding-left: 30px !important;
}

/* Style pour les icônes FontAwesome */
fa-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Notification styles */
.notification {
  background: #d4edda;
  color: #155724;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
  text-align: center;
  width:100%;
  font-weight: bold;
}

.notification.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Styles pour l'indicateur de chargement et les messages vides */
.loading-row, .empty-row {
  height: 100px;
}

.loading-row td, .empty-row td {
  text-align: center;
  vertical-align: middle;
  font-size: 18px;
  color: #6c757d;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(36, 150, 211, 0.2);
  border-top: 4px solid #2496d3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.text-center {
  text-align: center;
}

/* ✅ Titre */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3; /* ✅ Bleu ciel */
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ En-têtes */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3; /* ✅ Bleu ciel */
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Lignes */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Statuts */
.status {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 14px;
  display: inline-block;
  margin: 0 auto;
}

.status.sent {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  box-shadow: 0px 3px 8px rgba(40, 167, 69, 0.4);
}

/* Columns styling */
.action-col, .status-col, .rapport-col {
  min-width: 150px;
  text-align: center;
}

.action-col button, .status-col button, .status-col span, .rapport-col button, .rapport-col span {
  margin: 0 auto;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* Rapport status styles */
.rapport-status {
  margin-top: 8px;
  font-size: 12px;
  font-weight: bold;
}

.status-created {
  color: #28a745;
}

.status-not-created {
  color: #dc3545;
}

/* ✅ Boutons */
button {
  padding: 10px 15px;
  border: none;
  cursor: pointer;
  border-radius: 20px;
  font-weight: bold;
  margin: 5px auto;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
}

/* Bouton Détails */
.btn-details {
  background: #2496d3;
  color: white;
  box-shadow: 0px 4px 10px rgba(36, 150, 211, 0.3);
}

.btn-details:hover {
  background: #1e78b5;
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.6);
}

/* Bouton Action */
.btn-action {
  padding: 8px 12px;
  font-size: 13px;
  min-width: 130px;
}

.btn-send {
  background: #28a745;
  color: white;
  box-shadow: 0px 4px 10px rgba(40, 167, 69, 0.3);
}

.btn-send:hover:not(:disabled) {
  background: #218838;
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(40, 167, 69, 0.6);
}

/* Bouton "Créer Rapport" */
.btn-create {
  background-color: #1e78b5; /* Perfect blue */
  color: white;
  box-shadow: 0px 4px 10px rgba(30, 120, 181, 0.3);
}

.btn-create:hover:not(:disabled) {
  background-color: #1a6a9e; /* Darker blue on hover */
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(30, 120, 181, 0.6);
}

/* Bouton "Voir Rapport" */
.btn-view {
  background-color: #6c757d; /* Grey */
  color: white;
  box-shadow: 0px 4px 10px rgba(108, 117, 125, 0.3);
}

.btn-view:hover:not(:disabled) {
  background-color: #5a6268; /* Darker grey on hover */
  transform: scale(1.05);
  box-shadow: 0px 8px 20px rgba(108, 117, 125, 0.6);
}

/* Loading button style */
.loading-btn {
  opacity: 0.8;
  cursor: not-allowed;
}

/* Loading spinner animation */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Pending button style */
.pending-btn {
  background-color: #ffc107; /* Warning yellow */
  color: #212529;
  cursor: not-allowed;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ✅ Effets interactifs */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
}

/* ✅ Animation Glow */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}

/* Pagination styles */
.pagination-custom {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
}

.pagination-custom /deep/ .ngx-pagination li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-custom /deep/ .ngx-pagination .current {
  background: #2496d3;
  border-radius: 4px;
}

.pagination-custom /deep/ .ngx-pagination a:hover {
  background: rgba(36, 150, 211, 0.1);
  border-radius: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .demandes-container {
    padding: 30px 15px;
  }

  .filter-bar {
    max-width: 100%;
    flex-direction: column;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: 8px;
  }

  button {
    padding: 8px 12px;
    font-size: 14px;
  }

  .btn-action, .btn-details {
    width: 100%;
    min-width: 100px;
  }
}
