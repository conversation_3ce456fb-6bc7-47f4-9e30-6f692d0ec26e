<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Checking devis table structure...\n";
    
    $columns = DB::select('DESCRIBE devis');
    echo "Devis table columns:\n";
    echo "===================\n";
    
    foreach($columns as $column) {
        echo "- {$column->Field} ({$column->Type}) " . ($column->Null === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    echo "\nTotal columns: " . count($columns) . "\n";
    
    // Check if 'demande' column exists
    $hasDemandeColumn = false;
    foreach($columns as $column) {
        if ($column->Field === 'demande') {
            $hasDemandeColumn = true;
            break;
        }
    }
    
    if ($hasDemandeColumn) {
        echo "\n✅ 'demande' column exists in devis table\n";
    } else {
        echo "\n❌ 'demande' column does NOT exist in devis table\n";
        echo "This is causing the SQL error!\n";
    }
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
