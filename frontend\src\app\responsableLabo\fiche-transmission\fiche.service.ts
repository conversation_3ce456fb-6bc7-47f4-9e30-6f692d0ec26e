import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FicheTransmissionService {
  private apiUrl = 'http://127.0.0.1:8000/api/fiches';

  constructor(private http: HttpClient) {}

  // Fetch all fiches de transmission
  getAllFiches(): Observable<any> {
    return this.http.get<any>(`http://127.0.0.1:8000/api/fiches/analysts`);
  }

  // Get count of fiches without created reports
  getFichesWithoutReportsCount(): Observable<number> {
    return this.getAllFiches().pipe(
      map(fiches => {
        // Filter fiches with status 'sented' but without created reports
        const fichesSentedWithoutReports = fiches.filter((fiche: any) =>
          fiche.status === 'sented' && (!fiche.statusRapport || fiche.statusRapport === 'not_created')
        );
        return fichesSentedWithoutReports.length;
      })
    );
  }

  // Get count of fiches without results (result_id is null in demande)
  getFichesWithoutResultsCount(): Observable<number> {
    return this.getAllFiches().pipe(
      map(fiches => {
        // Filter fiches with status 'sented' but with null result_id in demande
        const fichesWithoutResults = fiches.filter((fiche: any) =>
          fiche.status === 'sented' &&
          fiche.demande &&
          (fiche.demande.result_id === null || fiche.demande.result_id === undefined)
        );
        return fichesWithoutResults.length;
      })
    );
  }
  getRapportIdByDemande(demandeId: string): Observable<any> {
    return this.http.get(`http://127.0.0.1:8000/api/rapport/id/demande/${demandeId}`);
  }
  sendFiche(ficheId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${ficheId}/send`, {});
  }
  // Fetch fiches related to a specific fiche_transmission_id
  getFichesByTransmissionId(ficheTransmissionId: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${ficheTransmissionId}`);
  }

  createFicheTransmission(demandeId: String): Observable<{ ficheId: number }> {
    console.log(demandeId);
    return this.http.post<{ ficheId: number }>(`${this.apiUrl}/create/${demandeId}`, {});
  }
  createRapport(demandeId: string): Observable<any> {
    return this.http.post(`http://127.0.0.1:8000/api/create-rapport/${demandeId}`, {});
  }
}
